#!/usr/bin/env python3
"""
Run script for the Hillview School Management System.
This script creates and runs the Flask application.
"""

import os
import sys

# Add the parent directory to the Python path so we can import new_structure as a package
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

try:
    # Import create_app from the new_structure package
    from new_structure import create_app

    # Create the Flask application
    app = create_app('development')

    # Check if HTTPS is requested
    use_https = '--https' in sys.argv or os.environ.get('FLASK_HTTPS') == 'true'

    if use_https:
        print("🚀 Hillview School Management System")
        print("📍 Server: https://localhost:5000")
        print("🔒 HTTPS Mode: Enabled for PWA testing")
        print("⚠️  You may see security warnings - click 'Advanced' and 'Proceed'")

        # Install pyOpenSSL if not available
        try:
            import ssl
            app.run(debug=True, host='0.0.0.0', port=5000, ssl_context='adhoc')
        except ImportError:
            print("❌ HTTPS requires pyOpenSSL. Install with: pip install pyOpenSSL")
            print("📍 Falling back to HTTP: http://localhost:5000")
            app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("🚀 Hillview School Management System")
        print("📍 Server: http://localhost:8080")
        print("🌐 Network: http://*************:8080")
        print("💡 For HTTPS (PWA testing): python run.py --https")
        print("🔧 Using port 8080 to avoid HTTPS conflicts")
        app.run(debug=True, host='0.0.0.0', port=8080)

except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
