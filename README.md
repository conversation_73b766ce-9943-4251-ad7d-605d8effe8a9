# Hillview School Management System

🏫 **Enterprise-Grade School Management Platform**

## 🚀 Quick Start

The application runs from the `new_structure` directory:

```bash
cd new_structure
python run.py
```

Access the application at: **http://localhost:5000**

## 🔐 Default Login Credentials

- **Username**: `headteacher`
- **Password**: `admin123`

## 📁 Project Structure

```
hillview_mvp/
├── new_structure/          # Main application (run from here)
│   ├── run.py             # Application entry point
│   ├── models/            # Database models
│   ├── views/             # Application routes
│   ├── services/          # Business logic
│   ├── templates/         # HTML templates
│   ├── static/            # CSS, JS, images
│   └── requirements.txt   # Dependencies
└── venv/                  # Python virtual environment
```

## 🎯 Key Features

- **Multi-role Access**: Headteacher, Class Teacher, Subject Teacher
- **Student Management**: Comprehensive student records
- **Grade Management**: Flexible grading system (CBC compliant)
- **Report Generation**: Automated PDF reports
- **Analytics Dashboard**: Performance insights
- **Security**: Enterprise-grade security features
- **Scalability**: Built for large-scale deployments

## 📖 Documentation

For detailed documentation, see:

- `new_structure/README.md` - Complete system documentation
- `new_structure/FINAL_VERIFICATION.md` - System verification guide

## 🛠️ Technologies

- **Backend**: Flask (Python)
- **Database**: MySQL/SQLite with SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, JavaScript
- **Security**: Custom security modules
- **Architecture**: MVC pattern with service layer
