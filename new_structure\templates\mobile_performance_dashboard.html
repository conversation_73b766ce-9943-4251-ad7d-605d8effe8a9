<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0">
    <meta name="description" content="Mobile Performance Dashboard - Hillview School Management System">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#667eea">
    
    <title>Mobile Performance Dashboard - Hillview School</title>
    
    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Mobile Responsive Dashboard Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile_responsive_dashboard.css') }}">
    
    <style>
        :root {
            --primary-color: #667eea;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --light-bg: #f9fafb;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f1e8 0%, #7dd3c0 50%, #4a9b8e 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .performance-navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .performance-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .performance-section {
            background: rgba(255, 255, 255, 0.95);
            margin: 1rem 0;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .performance-metric-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        
        .performance-metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }
        
        .metric-icon.primary { background: var(--primary-color); }
        .metric-icon.success { background: var(--success-color); }
        .metric-icon.warning { background: var(--warning-color); }
        .metric-icon.danger { background: var(--danger-color); }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .metric-change {
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .metric-change.positive { color: var(--success-color); }
        .metric-change.negative { color: var(--danger-color); }
        .metric-change.neutral { color: var(--text-secondary); }
        
        .chart-container {
            position: relative;
            width: 100%;
            height: 300px;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .optimization-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .optimization-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
        }
        
        .optimization-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--warning-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }
        
        .optimization-text {
            flex: 1;
            font-size: 0.9rem;
            color: var(--text-primary);
        }
        
        .device-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .device-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .device-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .device-count {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .device-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .refresh-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .refresh-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .device-breakdown {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .performance-container {
                padding: 0 0.75rem;
            }
            
            .chart-container {
                height: 250px;
                padding: 0.75rem;
            }
        }
        
        @media (max-width: 480px) {
            .device-breakdown {
                grid-template-columns: 1fr;
            }
            
            .metric-value {
                font-size: 1.5rem;
            }
            
            .performance-section {
                padding: 1rem;
                margin: 0.75rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Performance Dashboard Navigation -->
    <nav class="performance-navbar">
        <div class="performance-container">
            <div class="navbar-brand">
                <i class="fas fa-tachometer-alt"></i>
                <span>Hillview School - Mobile Performance Dashboard</span>
            </div>
            <div class="navbar-actions">
                <button class="refresh-btn" onclick="refreshMetrics()">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh</span>
                </button>
            </div>
        </div>

        <!-- Optimization Opportunities -->
        <div class="performance-section">
            <h2 class="section-title">
                <i class="fas fa-lightbulb"></i>
                Optimization Opportunities
            </h2>

            <ul class="optimization-list">
                <li class="optimization-item">
                    <div class="optimization-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="optimization-text">
                        Enable image optimization for mobile devices to reduce load times by up to 40%
                    </div>
                </li>
                <li class="optimization-item">
                    <div class="optimization-icon">
                        <i class="fas fa-compress"></i>
                    </div>
                    <div class="optimization-text">
                        Implement response compression to reduce mobile data usage and improve speed
                    </div>
                </li>
                <li class="optimization-item">
                    <div class="optimization-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="optimization-text">
                        Add mobile-specific caching for frequently accessed student and parent data
                    </div>
                </li>
                <li class="optimization-item">
                    <div class="optimization-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="optimization-text">
                        Minify CSS and JavaScript for mobile devices to reduce bundle size
                    </div>
                </li>
            </ul>
        </div>

        <!-- Slowest Endpoints -->
        <div class="performance-section">
            <h2 class="section-title">
                <i class="fas fa-stopwatch"></i>
                Slowest Endpoints (Mobile)
            </h2>

            <div class="chart-container">
                <canvas id="slowestEndpointsChart"></canvas>
            </div>
        </div>

        <!-- Performance Implementation Status -->
        <div class="performance-section">
            <h2 class="section-title">
                <i class="fas fa-cogs"></i>
                Performance Optimization Status
            </h2>

            <div class="metrics-grid">
                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon success">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="metric-value">✅</div>
                    <div class="metric-label">Hardware Acceleration</div>
                    <div class="metric-change positive">
                        <i class="fas fa-info-circle"></i> CSS transforms optimized
                    </div>
                </div>

                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon success">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="metric-value">✅</div>
                    <div class="metric-label">Touch Optimization</div>
                    <div class="metric-change positive">
                        <i class="fas fa-info-circle"></i> Touch events optimized
                    </div>
                </div>

                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon success">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="metric-value">✅</div>
                    <div class="metric-label">Smooth Scrolling</div>
                    <div class="metric-change positive">
                        <i class="fas fa-info-circle"></i> Webkit scrolling enabled
                    </div>
                </div>

                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="metric-value">⏳</div>
                    <div class="metric-label">Image Optimization</div>
                    <div class="metric-change neutral">
                        <i class="fas fa-info-circle"></i> In development
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializePerformanceCharts();
            startRealTimeUpdates();
        });

        // Initialize performance charts
        function initializePerformanceCharts() {
            // Performance Trend Chart
            const trendCtx = document.getElementById('performanceTrendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: 'Mobile Load Time (ms)',
                        data: [520, 480, 450, 520, 480, 420, 450],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Desktop Load Time (ms)',
                        data: [380, 360, 340, 380, 360, 320, 340],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Load Time (ms)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Time of Day'
                            }
                        }
                    }
                }
            });

            // Slowest Endpoints Chart
            const endpointsCtx = document.getElementById('slowestEndpointsChart').getContext('2d');
            new Chart(endpointsCtx, {
                type: 'bar',
                data: {
                    labels: ['/classteacher/analytics', '/parent/dashboard', '/classteacher/dashboard', '/student/reports', '/teacher/marks'],
                    datasets: [{
                        label: 'Average Load Time (ms)',
                        data: [1200, 980, 850, 720, 650],
                        backgroundColor: [
                            '#ef4444',
                            '#f59e0b',
                            '#f59e0b',
                            '#10b981',
                            '#10b981'
                        ],
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Load Time (ms)'
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    }
                }
            });
        }

        // Refresh metrics
        function refreshMetrics() {
            const refreshBtn = document.querySelector('.refresh-btn');
            const icon = refreshBtn.querySelector('i');

            // Add spinning animation
            icon.classList.add('fa-spin');
            refreshBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Update metrics with new values
                updateMetrics();

                // Remove spinning animation
                icon.classList.remove('fa-spin');
                refreshBtn.disabled = false;

                console.log('📊 Performance metrics refreshed');
            }, 1500);
        }

        // Update metrics with simulated data
        function updateMetrics() {
            // Generate random variations
            const baseValues = {
                avgMobileTime: 0.45,
                mobileRequests: 1247,
                slowRequests: 23,
                successRate: 99.2,
                mobileCount: 847,
                tabletCount: 234,
                desktopCount: 166
            };

            // Update with slight variations
            Object.keys(baseValues).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    let newValue = baseValues[key];

                    if (key === 'avgMobileTime') {
                        newValue = (baseValues[key] + (Math.random() - 0.5) * 0.1).toFixed(2) + 's';
                    } else if (key === 'successRate') {
                        newValue = (baseValues[key] + (Math.random() - 0.5) * 0.5).toFixed(1) + '%';
                    } else {
                        newValue = Math.floor(baseValues[key] + (Math.random() - 0.5) * 50);
                    }

                    element.textContent = newValue;
                }
            });
        }

        // Start real-time updates
        function startRealTimeUpdates() {
            // Update metrics every 30 seconds
            setInterval(() => {
                updateMetrics();
            }, 30000);
        }

        // Log performance information
        console.log('📱 Mobile Performance Dashboard initialized');
        console.log('🎯 Features: Real-time monitoring, optimization tracking, device analytics');
        console.log('⚡ Performance: Hardware acceleration, touch optimization, smooth scrolling');

        // Monitor page performance
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`📊 Dashboard loaded in ${loadTime.toFixed(2)}ms`);

            // Check if running on mobile
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                console.log('📱 Mobile device detected - performance optimizations active');
            }
        });
    </script>
</body>
</html>
    </nav>

    <div class="performance-container">
        <!-- Performance Overview -->
        <div class="performance-section">
            <h2 class="section-title">
                <i class="fas fa-chart-line"></i>
                Performance Overview (Last 24 Hours)
            </h2>
            
            <div class="metrics-grid">
                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon primary">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="avgMobileTime">0.45s</div>
                    <div class="metric-label">Average Mobile Load Time</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-down"></i> 12% faster than yesterday
                    </div>
                </div>
                
                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon success">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="mobileRequests">1,247</div>
                    <div class="metric-label">Mobile Requests</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> 8% increase
                    </div>
                </div>
                
                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="slowRequests">23</div>
                    <div class="metric-label">Slow Requests (>1s)</div>
                    <div class="metric-change negative">
                        <i class="fas fa-arrow-up"></i> 3 more than yesterday
                    </div>
                </div>
                
                <div class="performance-metric-card">
                    <div class="metric-header">
                        <div class="metric-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="metric-value" id="successRate">99.2%</div>
                    <div class="metric-label">Success Rate</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> 0.3% improvement
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="performance-section">
            <h2 class="section-title">
                <i class="fas fa-chart-bar"></i>
                Performance Trends
            </h2>
            
            <div class="chart-container">
                <canvas id="performanceTrendChart"></canvas>
            </div>
        </div>

        <!-- Device Breakdown -->
        <div class="performance-section">
            <h2 class="section-title">
                <i class="fas fa-devices"></i>
                Device Breakdown
            </h2>
            
            <div class="device-breakdown">
                <div class="device-item">
                    <div class="device-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="device-count" id="mobileCount">847</div>
                    <div class="device-label">Mobile</div>
                </div>
                
                <div class="device-item">
                    <div class="device-icon">
                        <i class="fas fa-tablet-alt"></i>
                    </div>
                    <div class="device-count" id="tabletCount">234</div>
                    <div class="device-label">Tablet</div>
                </div>
                
                <div class="device-item">
                    <div class="device-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="device-count" id="desktopCount">166</div>
                    <div class="device-label">Desktop</div>
                </div>
            </div>
        </div>
