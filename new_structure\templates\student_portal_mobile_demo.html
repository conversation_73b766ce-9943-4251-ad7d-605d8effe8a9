<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0"
    />
    <meta
      name="description"
      content="Student Portal Mobile Demo - Hillview School Management System"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#667eea" />

    <title>Student Portal Mobile Demo - Hillview School</title>

    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- Mobile Responsive Dashboard Styles -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/mobile_responsive_dashboard.css') }}"
    />

    <style>
      :root {
        --primary-color: #667eea;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --border-color: #e5e7eb;
        --light-bg: #f9fafb;
        --success-color: #10b981;
        --danger-color: #ef4444;
      }

      body {
        font-family: "Inter", sans-serif;
        background: #f8f9fa;
        margin: 0;
        padding: 0;
        min-height: 100vh;
      }

      .demo-section {
        background: white;
        margin: 1rem 0;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .demo-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
      }

      .status-complete {
        background: #dcfce7;
        color: #166534;
      }

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
      }

      .feature-card {
        padding: 1rem;
        background: var(--light-bg);
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
      }

      .feature-card h4 {
        margin: 0 0 0.5rem 0;
        color: var(--primary-color);
        font-size: 0.9rem;
      }

      .feature-card p {
        margin: 0;
        font-size: 0.8rem;
        color: var(--text-secondary);
      }

      /* Demo-specific styles */
      .demo-navbar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 0;
        margin-bottom: 2rem;
      }

      .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }

      @media (max-width: 768px) {
        .feature-grid {
          grid-template-columns: 1fr;
        }

        .demo-container {
          padding: 0 0.75rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Demo Navigation Bar -->
    <nav class="demo-navbar">
      <div class="demo-container">
        <div class="navbar-brand">
          <i class="fas fa-user-graduate"></i>
          <span>Hillview School - Student Portal Mobile Demo</span>
        </div>
        <ul class="navbar-nav">
          <li>
            <a href="#" class="nav-link"
              ><i class="fas fa-home"></i> <span>Dashboard</span></a
            >
          </li>
          <li>
            <a href="#" class="nav-link"
              ><i class="fas fa-chart-line"></i> <span>Reports</span></a
            >
          </li>
          <li>
            <a href="#" class="nav-link"
              ><i class="fas fa-calendar"></i> <span>Schedule</span></a
            >
          </li>
          <li>
            <a href="#" class="nav-link"
              ><i class="fas fa-user"></i> <span>Profile</span></a
            >
          </li>
          <li>
            <a href="#" class="nav-link logout-btn"
              ><i class="fas fa-sign-out-alt"></i> <span>Logout</span></a
            >
          </li>
        </ul>
      </div>
    </nav>

    <div class="demo-container">
      <!-- Implementation Status -->
      <div class="demo-section">
        <h2 class="demo-title">
          <i class="fas fa-check-circle"></i>
          Student Portal Mobile Implementation Status
        </h2>

        <div class="feature-grid">
          <div class="feature-card">
            <h4>
              <span class="status-badge status-complete"
                ><i class="fas fa-check"></i> Complete</span
              >
            </h4>
            <h4>Mobile-Responsive Student Dashboard</h4>
            <p>
              Touch-friendly student dashboard with mobile-optimized layout and
              navigation
            </p>
          </div>
          <div class="feature-card">
            <h4>
              <span class="status-badge status-complete"
                ><i class="fas fa-check"></i> Complete</span
              >
            </h4>
            <h4>Parent Portal Mobile Interface</h4>
            <p>
              Mobile-optimized parent portal with child management and progress
              tracking
            </p>
          </div>
          <div class="feature-card">
            <h4>
              <span class="status-badge status-complete"
                ><i class="fas fa-check"></i> Complete</span
              >
            </h4>
            <h4>Student Information Cards</h4>
            <p>
              Mobile-friendly student information display with touch-optimized
              interactions
            </p>
          </div>
          <div class="feature-card">
            <h4>
              <span class="status-badge status-complete"
                ><i class="fas fa-check"></i> Complete</span
              >
            </h4>
            <h4>Activity Tracking System</h4>
            <p>
              Mobile-responsive activity feeds and progress tracking for
              students and parents
            </p>
          </div>
        </div>
      </div>

      <!-- Student Dashboard Demo -->
      <div class="demo-section">
        <h2 class="demo-title">
          <i class="fas fa-user-graduate"></i>
          Mobile Student Dashboard Demo
        </h2>

        <div class="student-dashboard-container">
          <div class="student-welcome-section">
            <h1 class="student-welcome-title">Welcome back, Sarah Johnson!</h1>
            <p class="student-welcome-subtitle">Grade 8A • Hillview School</p>

            <div class="student-info-grid">
              <div class="student-info-item">
                <span class="student-info-label">Admission Number</span>
                <span class="student-info-value">STU2024001</span>
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Class</span>
                <span class="student-info-value">Grade 8 Stream A</span>
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Current Term</span>
                <span class="student-info-value">Term 1, 2024</span>
              </div>
              <div class="student-info-item">
                <span class="student-info-label">Overall Average</span>
                <span class="student-info-value">87.5%</span>
              </div>
            </div>
          </div>

          <div class="recent-activity-section">
            <h3 class="recent-activity-title">
              <i class="fas fa-clock"></i>
              Recent Activity
            </h3>

            <ul class="activity-list">
              <li class="activity-item">
                <div class="activity-icon">
                  <i class="fas fa-file-alt"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Mathematics Report Generated</div>
                  <div class="activity-details">
                    Mid-term assessment results available
                  </div>
                </div>
                <div class="activity-time">2 hours ago</div>
              </li>
              <li class="activity-item">
                <div class="activity-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Science Test Completed</div>
                  <div class="activity-details">
                    Score: 92% - Excellent performance
                  </div>
                </div>
                <div class="activity-time">1 day ago</div>
              </li>
              <li class="activity-item">
                <div class="activity-icon">
                  <i class="fas fa-book"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">English Assignment Submitted</div>
                  <div class="activity-details">
                    Essay on "Environmental Conservation"
                  </div>
                </div>
                <div class="activity-time">3 days ago</div>
              </li>
              <li class="activity-item">
                <div class="activity-icon">
                  <i class="fas fa-trophy"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Achievement Unlocked</div>
                  <div class="activity-details">
                    Top performer in Mathematics
                  </div>
                </div>
                <div class="activity-time">1 week ago</div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Parent Portal Demo -->
      <div class="demo-section">
        <h2 class="demo-title">
          <i class="fas fa-users"></i>
          Mobile Parent Portal Demo
        </h2>

        <div class="parent-dashboard-container">
          <div class="parent-welcome-section">
            <h1 class="parent-welcome-title">Welcome, Mr. & Mrs. Johnson</h1>
            <p class="parent-welcome-subtitle">
              Parent Portal • Hillview School
            </p>
          </div>

          <div class="children-grid">
            <div class="child-card">
              <div class="child-card-header">
                <h3 class="child-name">Sarah Johnson</h3>
                <span class="child-class">Grade 8A</span>
              </div>

              <div class="child-details-grid">
                <div class="child-detail-item">
                  <span class="child-detail-label">Admission Number</span>
                  <span class="child-detail-value">STU2024001</span>
                </div>
                <div class="child-detail-item">
                  <span class="child-detail-label">Current Average</span>
                  <span class="child-detail-value">87.5%</span>
                </div>
                <div class="child-detail-item">
                  <span class="child-detail-label">Attendance</span>
                  <span class="child-detail-value">95%</span>
                </div>
                <div class="child-detail-item">
                  <span class="child-detail-label">Last Report</span>
                  <span class="child-detail-value">Term 1 Mid-Term</span>
                </div>
              </div>

              <div class="child-actions">
                <a href="#" class="child-action-btn">
                  <i class="fas fa-chart-line"></i>
                  View Performance
                </a>
                <a href="#" class="child-action-btn secondary">
                  <i class="fas fa-file-alt"></i>
                  Download Reports
                </a>
              </div>
            </div>

            <div class="child-card">
              <div class="child-card-header">
                <h3 class="child-name">Michael Johnson</h3>
                <span class="child-class">Grade 6B</span>
              </div>

              <div class="child-details-grid">
                <div class="child-detail-item">
                  <span class="child-detail-label">Admission Number</span>
                  <span class="child-detail-value">STU2024002</span>
                </div>
                <div class="child-detail-item">
                  <span class="child-detail-label">Current Average</span>
                  <span class="child-detail-value">82.3%</span>
                </div>
                <div class="child-detail-item">
                  <span class="child-detail-label">Attendance</span>
                  <span class="child-detail-value">92%</span>
                </div>
                <div class="child-detail-item">
                  <span class="child-detail-label">Last Report</span>
                  <span class="child-detail-value">Term 1 Mid-Term</span>
                </div>
              </div>

              <div class="child-actions">
                <a href="#" class="child-action-btn">
                  <i class="fas fa-chart-line"></i>
                  View Performance
                </a>
                <a href="#" class="child-action-btn secondary">
                  <i class="fas fa-file-alt"></i>
                  Download Reports
                </a>
              </div>
            </div>
          </div>

          <div class="recent-activity-section">
            <h3 class="recent-activity-title">
              <i class="fas fa-bell"></i>
              Family Activity Feed
            </h3>

            <ul class="activity-list">
              <li class="activity-item">
                <div class="activity-icon">
                  <i class="fas fa-star"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Sarah - Top Performer Award</div>
                  <div class="activity-details">
                    Achieved highest score in Mathematics test
                  </div>
                </div>
                <div class="activity-time">Today</div>
              </li>
              <li class="activity-item">
                <div class="activity-icon">
                  <i class="fas fa-file-alt"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Michael - Report Available</div>
                  <div class="activity-details">
                    Mid-term assessment report ready for download
                  </div>
                </div>
                <div class="activity-time">2 days ago</div>
              </li>
              <li class="activity-item">
                <div class="activity-icon">
                  <i class="fas fa-calendar"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Parent-Teacher Meeting</div>
                  <div class="activity-details">
                    Scheduled for next Friday at 2:00 PM
                  </div>
                </div>
                <div class="activity-time">1 week ago</div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Mobile Features Demo -->
      <div class="demo-section">
        <h2 class="demo-title">
          <i class="fas fa-mobile-alt"></i>
          Mobile-Specific Features Demo
        </h2>

        <div class="feature-grid">
          <div class="feature-card">
            <h4><i class="fas fa-touch"></i> Touch-Friendly Navigation</h4>
            <p>
              All navigation elements are optimized for touch interaction with
              proper sizing and spacing
            </p>
          </div>
          <div class="feature-card">
            <h4><i class="fas fa-expand-arrows-alt"></i> Responsive Layout</h4>
            <p>
              Content adapts seamlessly from desktop to tablet to mobile with
              optimized layouts
            </p>
          </div>
          <div class="feature-card">
            <h4><i class="fas fa-eye"></i> Mobile-First Design</h4>
            <p>
              Designed primarily for mobile devices with progressive enhancement
              for larger screens
            </p>
          </div>
          <div class="feature-card">
            <h4><i class="fas fa-bolt"></i> Fast Performance</h4>
            <p>
              Optimized for mobile performance with efficient loading and smooth
              animations
            </p>
          </div>
        </div>
      </div>

      <!-- Testing Instructions -->
      <div class="demo-section">
        <h2 class="demo-title">
          <i class="fas fa-clipboard-check"></i>
          Mobile Testing Instructions
        </h2>

        <div
          style="
            background: #f0f9ff;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
          "
        >
          <h4 style="margin-top: 0; color: #1e40af">
            How to Test Student Portal Mobile Features:
          </h4>
          <ol style="color: #475569; line-height: 1.6">
            <li>
              <strong>Navigation Testing:</strong> Test touch interactions with
              navigation elements and buttons
            </li>
            <li>
              <strong>Card Interactions:</strong> Tap student/child cards and
              verify hover effects work on mobile
            </li>
            <li>
              <strong>Activity Feed:</strong> Scroll through activity lists and
              test touch interactions
            </li>
            <li>
              <strong>Responsive Layout:</strong> Test on different screen sizes
              and orientations
            </li>
            <li>
              <strong>Touch Targets:</strong> Verify all interactive elements
              are easily tappable (44px+)
            </li>
            <li>
              <strong>Form Interactions:</strong> Test any form inputs and
              button interactions
            </li>
            <li>
              <strong>Performance:</strong> Check loading speed and smooth
              scrolling on mobile devices
            </li>
          </ol>
        </div>
      </div>

      <!-- Implementation Summary -->
      <div class="demo-section">
        <h2 class="demo-title">
          <i class="fas fa-code"></i>
          Implementation Summary
        </h2>

        <div
          style="
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
          "
        >
          <h4 style="margin-top: 0; color: var(--primary-color)">
            Mobile Optimization Features Implemented:
          </h4>
          <ul style="color: #475569; line-height: 1.6; margin-bottom: 0">
            <li>
              ✅ <strong>Mobile-responsive CSS</strong> added to
              parent_login.html and parent_dashboard.html
            </li>
            <li>
              ✅ <strong>Student portal navigation</strong> with touch-friendly
              interactions
            </li>
            <li>
              ✅ <strong>Parent portal dashboard</strong> with mobile-optimized
              child cards
            </li>
            <li>
              ✅ <strong>Activity tracking system</strong> with
              mobile-responsive layout
            </li>
            <li>
              ✅ <strong>Student information cards</strong> with touch-optimized
              interactions
            </li>
            <li>
              ✅ <strong>Responsive grid systems</strong> that adapt to mobile
              screens
            </li>
            <li>
              ✅ <strong>Touch-friendly buttons</strong> with proper sizing and
              feedback
            </li>
            <li>
              ✅ <strong>Mobile-first typography</strong> and spacing
              optimization
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      // Mobile-specific interactions
      document.addEventListener("DOMContentLoaded", function () {
        setupMobileInteractions();
        logViewportInfo();
      });

      // Setup mobile touch interactions
      function setupMobileInteractions() {
        // Add touch feedback to child cards
        document.querySelectorAll(".child-card").forEach((card) => {
          card.addEventListener("touchstart", function () {
            this.style.transform = "scale(0.98)";
          });

          card.addEventListener("touchend", function () {
            setTimeout(() => {
              this.style.transform = "";
            }, 150);
          });
        });

        // Add touch feedback to activity items
        document.querySelectorAll(".activity-item").forEach((item) => {
          item.addEventListener("touchstart", function () {
            this.style.backgroundColor = "#f0f9ff";
          });

          item.addEventListener("touchend", function () {
            setTimeout(() => {
              this.style.backgroundColor = "";
            }, 150);
          });
        });

        // Add touch feedback to action buttons
        document.querySelectorAll(".child-action-btn").forEach((btn) => {
          btn.addEventListener("touchstart", function () {
            this.style.transform = "scale(0.95)";
          });

          btn.addEventListener("touchend", function () {
            setTimeout(() => {
              this.style.transform = "";
            }, 150);
          });
        });

        console.log("✅ Mobile touch interactions initialized");
      }

      // Log viewport information
      function logViewportInfo() {
        const size =
          window.innerWidth <= 480
            ? "Small Mobile"
            : window.innerWidth <= 768
            ? "Mobile/Tablet"
            : "Desktop";
        document.title = `Student Portal Demo - ${size} (${window.innerWidth}px)`;

        console.log(
          `📱 Viewport: ${window.innerWidth}x${window.innerHeight} (${size})`
        );
        console.log("🎯 Student Portal Mobile Demo loaded successfully!");
        console.log(
          "📋 Features: Touch interactions, responsive layout, mobile navigation"
        );
      }

      // Update viewport info on resize
      window.addEventListener("resize", logViewportInfo);

      // Button click handlers with visual feedback
      document
        .querySelectorAll(".child-action-btn, .nav-link")
        .forEach((element) => {
          element.addEventListener("click", function (e) {
            e.preventDefault();
            console.log("Button/Link clicked:", this.textContent.trim());

            // Visual feedback
            const originalTransform = this.style.transform;
            this.style.transform = "scale(0.95)";
            setTimeout(() => {
              this.style.transform = originalTransform;
            }, 150);
          });
        });
    </script>
  </body>
</html>
