{"authentication": [{"test_name": "<PERSON><PERSON>", "status": "PASS", "details": "Status: 302", "timestamp": "2025-07-13T16:58:35.209268"}, {"test_name": "Invalid Login Rejection", "status": "PASS", "details": "Properly rejected with status: 200", "timestamp": "2025-07-13T16:58:37.299042"}, {"test_name": "SQL Injection Protection (admin' OR '1'='1...)", "status": "PASS", "details": "SQL injection attempt blocked", "timestamp": "2025-07-13T16:58:39.346780"}, {"test_name": "SQL Injection Protection (admin'; DROP TABLE t...)", "status": "PASS", "details": "SQL injection attempt blocked", "timestamp": "2025-07-13T16:58:41.402049"}, {"test_name": "SQL Injection Protection (admin' UNION SELECT ...)", "status": "PASS", "details": "SQL injection attempt blocked", "timestamp": "2025-07-13T16:58:43.439049"}, {"test_name": "Security Headers Present", "status": "PASS", "details": "All security headers found", "timestamp": "2025-07-13T16:58:45.501218"}], "authorization": [{"test_name": "Unauthorized Access Block (/headteacher/)", "status": "PASS", "details": "Access denied with status: 302", "timestamp": "2025-07-13T16:58:51.996746"}, {"test_name": "Unauthorized Access Block (/classteacher/)", "status": "PASS", "details": "Access denied with status: 302", "timestamp": "2025-07-13T16:58:54.034008"}, {"test_name": "Unauthorized Access Block (/teacher/)", "status": "PASS", "details": "Access denied with status: 302", "timestamp": "2025-07-13T16:58:56.102722"}, {"test_name": "Unauthorized Access Block (/manage_students)", "status": "FAIL", "details": "Unexpected access granted: 404", "timestamp": "2025-07-13T16:58:58.147678"}, {"test_name": "Unauthorized Access Block (/manage_teachers)", "status": "FAIL", "details": "Unexpected access granted: 404", "timestamp": "2025-07-13T16:59:00.200393"}, {"test_name": "Headteacher Role Access", "status": "PASS", "details": "Headteacher can access admin functions", "timestamp": "2025-07-13T16:59:02.368621"}], "input_validation": [{"test_name": "XSS Protection (<script>alert('XSS')...)", "status": "PASS", "details": "XSS payload properly handled", "timestamp": "2025-07-13T16:59:08.903012"}, {"test_name": "XSS Protection (javascript:alert('XS...)", "status": "PASS", "details": "XSS payload properly handled", "timestamp": "2025-07-13T16:59:10.939225"}, {"test_name": "XSS Protection (<img src=x onerror=a...)", "status": "PASS", "details": "XSS payload properly handled", "timestamp": "2025-07-13T16:59:12.992425"}, {"test_name": "XSS Protection (';alert('XSS');//...)", "status": "PASS", "details": "XSS payload properly handled", "timestamp": "2025-07-13T16:59:15.054686"}, {"test_name": "Path Traversal Protection (../../../etc/passwd...)", "status": "PASS", "details": "Path traversal blocked: 403", "timestamp": "2025-07-13T16:59:17.116715"}, {"test_name": "Path Traversal Protection (..\\..\\..\\windows\\sys...)", "status": "PASS", "details": "Path traversal blocked: 403", "timestamp": "2025-07-13T16:59:19.157447"}, {"test_name": "Path Traversal Protection (%2e%2e%2f%2e%2e%2f%2...)", "status": "PASS", "details": "Path traversal blocked: 403", "timestamp": "2025-07-13T16:59:21.214137"}, {"test_name": "Path Traversal Protection (....//....//....//et...)", "status": "PASS", "details": "Path traversal blocked: 403", "timestamp": "2025-07-13T16:59:23.243523"}], "csrf_protection": [{"test_name": "CSRF Token Present", "status": "WARNING", "details": "CSRF tokens not clearly visible in forms", "timestamp": "2025-07-13T16:59:29.735497"}, {"test_name": "CSRF Protection Active", "status": "WARNING", "details": "POST may have succeeded without CSRF: 404", "timestamp": "2025-07-13T16:59:31.779749"}], "security_headers": [], "file_upload": [], "path_traversal": []}