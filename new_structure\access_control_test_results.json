[{"test_name": "headteacher Access to /headteacher/", "status": "PASS", "details": "Allowed access: 200", "timestamp": "2025-07-13T17:01:30.210831"}, {"test_name": "headteacher Access to /admin/", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:32.273366"}, {"test_name": "headteacher Access to /universal/", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:34.324268"}, {"test_name": "headteacher Access to /manage_teachers", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:36.357200"}, {"test_name": "headteacher Access to /manage_students", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:38.400696"}, {"test_name": "headteacher Access to /manage_subjects", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:40.430718"}, {"test_name": "headteacher Access to /analytics", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:42.475668"}, {"test_name": "headteacher Access to /school-setup/", "status": "PASS", "details": "Allowed access: 200", "timestamp": "2025-07-13T17:01:44.605725"}, {"test_name": "headteacher Access to /permission/", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:46.651970"}, {"test_name": "headteacher Access to /staff/", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:48.686044"}, {"test_name": "headteacher Access to /bulk_assignments/", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:01:50.729010"}, {"test_name": "classteacher Session Creation", "status": "FAIL", "details": "Could not create authenticated session", "timestamp": "2025-07-13T17:01:50.730021"}, {"test_name": "teacher Session Creation", "status": "FAIL", "details": "Could not create authenticated session", "timestamp": "2025-07-13T17:01:50.731024"}, {"test_name": "unauthenticated Access to /", "status": "PASS", "details": "Allowed access: 200", "timestamp": "2025-07-13T17:01:52.791448"}, {"test_name": "unauthenticated Access to /admin_login", "status": "PASS", "details": "Allowed access: 200", "timestamp": "2025-07-13T17:01:54.874888"}, {"test_name": "unauthenticated Access to /teacher_login", "status": "PASS", "details": "Allowed access: 200", "timestamp": "2025-07-13T17:01:56.953346"}, {"test_name": "unauthenticated Access to /classteacher_login", "status": "PASS", "details": "Allowed access: 200", "timestamp": "2025-07-13T17:01:59.035411"}, {"test_name": "unauthenticated Access to /static/", "status": "FAIL", "details": "Unexpected denial: 404", "timestamp": "2025-07-13T17:02:01.101496"}, {"test_name": "unauthenticated Access to /health", "status": "PASS", "details": "Allowed access: 200", "timestamp": "2025-07-13T17:02:03.168370"}, {"test_name": "unauthenticated Denial of /headteacher/", "status": "PASS", "details": "<PERSON><PERSON><PERSON> denied: 302", "timestamp": "2025-07-13T17:02:05.223048"}, {"test_name": "unauthenticated Denial of /classteacher/", "status": "PASS", "details": "<PERSON><PERSON><PERSON> denied: 302", "timestamp": "2025-07-13T17:02:07.275466"}, {"test_name": "unauthenticated Denial of /teacher/", "status": "PASS", "details": "<PERSON><PERSON><PERSON> denied: 302", "timestamp": "2025-07-13T17:02:09.348293"}, {"test_name": "unauthenticated Denial of /manage_students", "status": "FAIL", "details": "Unexpected access: 404", "timestamp": "2025-07-13T17:02:11.418850"}, {"test_name": "unauthenticated Denial of /manage_teachers", "status": "FAIL", "details": "Unexpected access: 404", "timestamp": "2025-07-13T17:02:13.452767"}, {"test_name": "unauthenticated Denial of /analytics", "status": "FAIL", "details": "Unexpected access: 404", "timestamp": "2025-07-13T17:02:15.512497"}, {"test_name": "Parameter Manipulation (role)", "status": "FAIL", "details": "May have granted elevated privileges", "timestamp": "2025-07-13T17:02:22.213228"}, {"test_name": "Parameter Manipulation (user_role)", "status": "FAIL", "details": "May have granted elevated privileges", "timestamp": "2025-07-13T17:02:24.372007"}, {"test_name": "Parameter Manipulation (access_level)", "status": "FAIL", "details": "May have granted elevated privileges", "timestamp": "2025-07-13T17:02:26.569541"}, {"test_name": "Parameter Manipulation (is_admin)", "status": "FAIL", "details": "May have granted elevated privileges", "timestamp": "2025-07-13T17:02:28.760231"}, {"test_name": "Parameter Manipulation (permissions)", "status": "FAIL", "details": "May have granted elevated privileges", "timestamp": "2025-07-13T17:02:31.001233"}, {"test_name": "Dangerous Operation Protection", "status": "PASS", "details": "Dangerous operation blocked: 404", "timestamp": "2025-07-13T17:02:33.036648"}, {"test_name": "IDOR - Student Record Access", "status": "PASS", "details": "Legitimate access handled properly: 404", "timestamp": "2025-07-13T17:02:39.608487"}, {"test_name": "IDOR Protection - Student Record Access", "status": "PASS", "details": "Unauthorized object access blocked: 404", "timestamp": "2025-07-13T17:02:41.657074"}, {"test_name": "IDOR - Teacher Record Access", "status": "PASS", "details": "Legitimate access handled properly: 404", "timestamp": "2025-07-13T17:02:43.683557"}, {"test_name": "IDOR Protection - Teacher Record Access", "status": "PASS", "details": "Unauthorized object access blocked: 404", "timestamp": "2025-07-13T17:02:45.733860"}, {"test_name": "IDOR - Grade Record Access", "status": "PASS", "details": "Legitimate access handled properly: 404", "timestamp": "2025-07-13T17:02:47.793865"}, {"test_name": "IDOR Protection - Grade Record Access", "status": "PASS", "details": "Unauthorized object access blocked: 404", "timestamp": "2025-07-13T17:02:49.836163"}, {"test_name": "IDOR - Stream Record Access", "status": "PASS", "details": "Legitimate access handled properly: 404", "timestamp": "2025-07-13T17:02:51.884102"}, {"test_name": "IDOR Protection - Stream Record Access", "status": "PASS", "details": "Unauthorized object access blocked: 404", "timestamp": "2025-07-13T17:02:53.928205"}]