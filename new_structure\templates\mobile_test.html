<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Responsive Test - Hillview School</title>
    
    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern_classteacher.css') }}">
    
    <!-- Mobile Responsive Login Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile_responsive_login.css') }}">
    
    <style>
        /* Test page specific styles */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .device-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .device-frame {
            border: 3px solid #374151;
            border-radius: 1rem;
            padding: 1rem;
            background: #f9fafb;
            position: relative;
        }
        
        .device-label {
            position: absolute;
            top: -0.5rem;
            left: 1rem;
            background: #374151;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .responsive-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 400px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        /* Simulate different screen sizes */
        .mobile-small .responsive-demo {
            max-width: 360px;
            margin: 0 auto;
        }
        
        .mobile-medium .responsive-demo {
            max-width: 480px;
            margin: 0 auto;
        }
        
        .tablet .responsive-demo {
            max-width: 768px;
            margin: 0 auto;
        }
        
        .desktop .responsive-demo {
            max-width: 100%;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: #f0f9ff;
            border-radius: 0.5rem;
            border-left: 4px solid #0ea5e9;
        }
        
        .feature-item i {
            color: #0ea5e9;
            font-size: 1.2rem;
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .test-btn.primary {
            background: #dc2626;
            color: white;
        }
        
        .test-btn.secondary {
            background: #6b7280;
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        @media (max-width: 768px) {
            .test-container {
                padding: 1rem;
                margin: 1rem;
            }
            
            .test-section {
                padding: 1rem;
            }
            
            .device-preview {
                grid-template-columns: 1fr;
            }
            
            .test-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 2rem;">
            <i class="fas fa-mobile-alt"></i>
            Mobile Responsive Login Test
        </h1>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-check-circle" style="color: #10b981;"></i>
                Mobile Responsive Features Implemented
            </h2>
            
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-mobile-alt"></i>
                    <span>Mobile-first responsive design</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-hand-pointer"></i>
                    <span>Touch-friendly 44px+ touch targets</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-expand-arrows-alt"></i>
                    <span>Flexible container sizing</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-text-height"></i>
                    <span>Scalable typography</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-palette"></i>
                    <span>Optimized spacing & padding</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-rotate"></i>
                    <span>Landscape orientation support</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-eye"></i>
                    <span>High DPI/Retina display support</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-universal-access"></i>
                    <span>Accessibility improvements</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-devices" style="color: #8b5cf6;"></i>
                Device Preview Simulation
            </h2>
            
            <div class="device-preview">
                <div class="device-frame mobile-small">
                    <div class="device-label">Mobile Small (360px)</div>
                    <div class="responsive-demo">
                        <div style="text-align: center; color: white;">
                            <i class="fas fa-mobile-alt" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <h3>Small Mobile</h3>
                            <p>Optimized for 360px screens</p>
                        </div>
                    </div>
                </div>
                
                <div class="device-frame mobile-medium">
                    <div class="device-label">Mobile Medium (480px)</div>
                    <div class="responsive-demo">
                        <div style="text-align: center; color: white;">
                            <i class="fas fa-mobile-alt" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <h3>Medium Mobile</h3>
                            <p>Optimized for 480px screens</p>
                        </div>
                    </div>
                </div>
                
                <div class="device-frame tablet">
                    <div class="device-label">Tablet (768px)</div>
                    <div class="responsive-demo">
                        <div style="text-align: center; color: white;">
                            <i class="fas fa-tablet-alt" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <h3>Tablet</h3>
                            <p>Optimized for 768px screens</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-vial" style="color: #f59e0b;"></i>
                Test Login Pages
            </h2>
            
            <p style="margin-bottom: 2rem; color: #6b7280;">
                Test the mobile responsive login pages on different devices and screen sizes:
            </p>
            
            <div class="test-buttons">
                <a href="{{ url_for('auth.admin_login') }}" class="test-btn primary">
                    <i class="fas fa-user-shield"></i>
                    Admin Login
                </a>
                <a href="{{ url_for('auth.teacher_login') }}" class="test-btn primary">
                    <i class="fas fa-chalkboard-teacher"></i>
                    Teacher Login
                </a>
                <a href="{{ url_for('auth.classteacher_login') }}" class="test-btn primary">
                    <i class="fas fa-users"></i>
                    Class Teacher Login
                </a>
                <a href="{{ url_for('auth.index') }}" class="test-btn secondary">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-info-circle" style="color: #3b82f6;"></i>
                Testing Instructions
            </h2>
            
            <div style="background: #f8fafc; padding: 1.5rem; border-radius: 0.5rem; border-left: 4px solid #3b82f6;">
                <h4 style="margin-top: 0; color: #1e40af;">How to Test Mobile Responsiveness:</h4>
                <ol style="color: #475569; line-height: 1.6;">
                    <li><strong>Browser Developer Tools:</strong> Press F12, click device toolbar icon, select different devices</li>
                    <li><strong>Resize Browser:</strong> Manually resize your browser window to test different widths</li>
                    <li><strong>Real Devices:</strong> Test on actual mobile phones and tablets</li>
                    <li><strong>Orientation:</strong> Test both portrait and landscape orientations</li>
                    <li><strong>Touch Interactions:</strong> Verify all buttons and inputs are easily tappable</li>
                </ol>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive testing functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Log viewport dimensions
            function logViewport() {
                console.log('Viewport:', window.innerWidth + 'x' + window.innerHeight);
            }
            
            // Log on resize
            window.addEventListener('resize', logViewport);
            logViewport();
            
            // Add click handlers for test buttons
            document.querySelectorAll('.test-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    console.log('Testing:', this.textContent.trim());
                });
            });
        });
    </script>
</body>
</html>
