/*
 * Mobile Responsive Dashboard Styles for Hillview School Management System
 * Comprehensive mobile-first responsive design for dashboard pages
 * Performance optimized for mobile devices
 */

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */

/* Hardware acceleration for smooth animations */
@media (max-width: 768px) {
  * {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
  }

  /* Optimize touch interactions */
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Enable smooth scrolling */
  html {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Optimize font rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Reduce repaints and reflows */
  .navbar,
  .dashboard-container,
  .modern-card,
  .metric-card,
  .child-card {
    will-change: transform;
    contain: layout style paint;
  }

  /* Optimize animations for 60fps */
  .modern-card:hover,
  .metric-card:hover,
  .child-card:hover {
    transform: translateY(-2px) translateZ(0);
    transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Reduce animation complexity on mobile */
  .analytics-tab,
  .modern-btn,
  .child-action-btn {
    transition: background-color 0.15s ease, transform 0.1s ease;
  }

  /* Optimize image rendering */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
    -ms-interpolation-mode: nearest-neighbor;
  }
}

/* ===== DESKTOP NAVIGATION BAR FIX ===== */

/* Fix horizontal scrolling issue on desktop */
@media (min-width: 769px) {
  .navbar {
    padding: 0.5rem 0 !important;
  }

  .navbar .container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    max-width: 100% !important;
    padding: 0 1rem !important;
  }

  .navbar-brand {
    flex-shrink: 0 !important;
    max-width: 250px !important;
    margin-right: 1rem !important;
  }

  .navbar-brand span {
    font-size: 1rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .navbar-nav {
    display: flex !important;
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    flex: 1 !important;
    justify-content: flex-end !important;
    gap: 0.25rem !important;
    margin: 0 !important;
    padding: 0 !important;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE/Edge */
  }

  .navbar-nav::-webkit-scrollbar {
    display: none !important; /* Chrome/Safari */
  }

  .navbar-nav li {
    flex-shrink: 0 !important;
    white-space: nowrap !important;
  }

  .navbar-nav .nav-link {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.85rem !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
  }

  .navbar-nav .nav-link i {
    font-size: 0.8rem !important;
    margin-right: 0.4rem !important;
  }

  .navbar-nav .logout-btn {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #ef4444 !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
    margin-left: 0.5rem !important;
    font-weight: 600 !important;
  }

  .navbar-nav .logout-btn:hover {
    background: #ef4444 !important;
    color: white !important;
  }
}

/* Medium desktop - more compact navigation */
@media (min-width: 769px) and (max-width: 1199px) {
  .navbar-brand {
    max-width: 200px !important;
  }

  .navbar-brand span {
    font-size: 0.9rem !important;
  }

  .navbar-nav .nav-link {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.8rem !important;
  }

  .navbar-nav .nav-link i {
    font-size: 0.75rem !important;
    margin-right: 0.3rem !important;
  }

  /* Hide text on very compact screens, show only icons */
  @media (max-width: 1024px) {
    .navbar-nav .nav-link span {
      display: none !important;
    }

    .navbar-nav .nav-link {
      padding: 0.5rem !important;
      min-width: 40px !important;
      text-align: center !important;
    }

    .navbar-nav .nav-link i {
      margin-right: 0 !important;
      font-size: 0.9rem !important;
    }
  }
}

/* Large desktop optimization */
@media (min-width: 1200px) {
  .navbar-nav {
    gap: 0.5rem !important;
  }

  .navbar-nav .nav-link {
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem !important;
  }

  .navbar-nav .nav-link i {
    font-size: 0.85rem !important;
    margin-right: 0.5rem !important;
  }
}

/* ===== STEP 1: NAVIGATION BAR MOBILE RESPONSIVENESS ===== */

/* Mobile Navigation Base Styles */
@media (max-width: 768px) {
  .navbar {
    padding: 0.75rem 0 !important;
    position: relative !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  .navbar .container {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding: 0 1rem !important;
  }

  .navbar-brand {
    width: 100% !important;
    margin-bottom: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  .navbar-brand span {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
  }

  .navbar-brand img {
    height: 32px !important;
    margin-right: 8px !important;
  }

  .navbar-nav {
    width: 100% !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .navbar-nav li {
    flex: 1 1 auto !important;
    min-width: 120px !important;
    max-width: 150px !important;
  }

  .navbar-nav .nav-link {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    padding: 0.5rem 0.25rem !important;
    font-size: 0.75rem !important;
    text-align: center !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
    min-height: 60px !important;
    justify-content: center !important;
  }

  .navbar-nav .nav-link i {
    font-size: 1.2rem !important;
    margin-bottom: 0.25rem !important;
    display: block !important;
  }

  .navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px) !important;
  }
}

/* Small Mobile Navigation (480px and below) */
@media (max-width: 480px) {
  .navbar-brand span {
    font-size: 1rem !important;
  }

  .navbar-nav li {
    min-width: 100px !important;
    max-width: 120px !important;
  }

  .navbar-nav .nav-link {
    font-size: 0.7rem !important;
    padding: 0.4rem 0.2rem !important;
    min-height: 55px !important;
  }

  .navbar-nav .nav-link i {
    font-size: 1.1rem !important;
  }
}

/* ===== STEP 2: DASHBOARD CONTAINER MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem 0.75rem !important;
    margin: 0 !important;
    max-width: 100% !important;
  }

  .container {
    padding: 0 0.75rem !important;
    margin: 0 auto !important;
    max-width: 100% !important;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 0.75rem 0.5rem !important;
  }

  .container {
    padding: 0 0.5rem !important;
  }
}

/* ===== STEP 3: DASHBOARD HEADER MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .dashboard-header {
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
    border-radius: 12px !important;
  }

  .header-content {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    gap: 1rem !important;
  }

  .header-info {
    width: 100% !important;
    margin-bottom: 0 !important;
  }

  .dashboard-title {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.2 !important;
  }

  .dashboard-title i {
    font-size: 1.3rem !important;
    margin-right: 0.5rem !important;
  }

  .dashboard-subtitle {
    font-size: 0.9rem !important;
    margin-bottom: 0 !important;
    opacity: 0.8 !important;
  }

  .dashboard-subtitle i {
    font-size: 0.8rem !important;
    margin-right: 0.3rem !important;
  }

  .header-actions {
    width: 100% !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
  }

  .header-actions .action-btn {
    flex: 1 1 auto !important;
    min-width: 120px !important;
    max-width: 150px !important;
    padding: 0.6rem 0.8rem !important;
    font-size: 0.8rem !important;
  }

  .header-actions .action-btn i {
    font-size: 0.9rem !important;
    margin-right: 0.3rem !important;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  .dashboard-title {
    font-size: 1.3rem !important;
  }

  .dashboard-subtitle {
    font-size: 0.8rem !important;
  }

  .header-actions .action-btn {
    min-width: 100px !important;
    max-width: 130px !important;
    padding: 0.5rem 0.6rem !important;
    font-size: 0.75rem !important;
  }
}

/* ===== STEP 4: DASHBOARD ACTIONS MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .dashboard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.75rem !important;
    margin-bottom: 1.5rem !important;
    justify-content: center !important;
  }

  .dashboard-actions .action-btn {
    flex: 1 1 auto !important;
    min-width: 140px !important;
    max-width: 180px !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.85rem !important;
    text-align: center !important;
    border-radius: 10px !important;
  }

  .dashboard-actions .action-btn i {
    font-size: 1rem !important;
    margin-right: 0.5rem !important;
    display: inline-block !important;
  }

  .dashboard-actions .action-btn span {
    display: inline !important;
  }
}

@media (max-width: 480px) {
  .dashboard-actions {
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
  }

  .dashboard-actions .action-btn {
    min-width: 120px !important;
    max-width: 150px !important;
    padding: 0.6rem 0.8rem !important;
    font-size: 0.8rem !important;
  }

  .dashboard-actions .action-btn i {
    font-size: 0.9rem !important;
    margin-right: 0.3rem !important;
  }
}

/* ===== STEP 5: RESPONSIVE GRID SYSTEM ===== */

@media (max-width: 768px) {
  /* Convert multi-column layouts to single column */
  .row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .col,
  .col-md-6,
  .col-lg-4,
  .col-lg-6,
  .col-lg-8,
  .col-xl-3,
  .col-xl-4,
  .col-xl-6,
  .col-xl-8 {
    width: 100% !important;
    flex: none !important;
    margin-bottom: 1rem !important;
  }

  /* Card layouts */
  .card,
  .chart-card,
  .stats-card {
    margin-bottom: 1rem !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }
}

/* ===== STEP 6: TOUCH-FRIENDLY INTERACTIONS ===== */

@media (max-width: 768px) {
  /* Ensure all interactive elements are touch-friendly */
  button,
  .btn,
  .action-btn,
  .nav-link,
  a {
    min-height: 44px !important;
    min-width: 44px !important;
    touch-action: manipulation !important;
  }

  /* Remove hover effects on touch devices */
  .action-btn:hover,
  .nav-link:hover,
  .btn:hover {
    transform: none !important;
  }

  /* Add active states for touch feedback */
  .action-btn:active,
  .nav-link:active,
  .btn:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
}

/* ===== STEP 7: TYPOGRAPHY SCALING ===== */

@media (max-width: 768px) {
  h1 {
    font-size: 1.5rem !important;
  }
  h2 {
    font-size: 1.3rem !important;
  }
  h3 {
    font-size: 1.1rem !important;
  }
  h4 {
    font-size: 1rem !important;
  }
  h5 {
    font-size: 0.9rem !important;
  }
  h6 {
    font-size: 0.8rem !important;
  }

  p,
  span,
  div {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.3rem !important;
  }
  h2 {
    font-size: 1.2rem !important;
  }
  h3 {
    font-size: 1rem !important;
  }
  h4 {
    font-size: 0.95rem !important;
  }
  h5 {
    font-size: 0.85rem !important;
  }
  h6 {
    font-size: 0.8rem !important;
  }

  p,
  span,
  div {
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
  }
}

/* ===== STEP 8: CHARTS CONTAINER MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .charts-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    margin-bottom: 2rem !important;
  }

  .chart-card {
    width: 100% !important;
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    background: white !important;
  }

  .chart-card h3 {
    font-size: 1.1rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
    color: var(--text-primary) !important;
  }

  .chart-wrapper {
    position: relative !important;
    height: 250px !important;
    width: 100% !important;
    margin: 0 auto !important;
  }

  .chart-wrapper canvas {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Heatmap mobile optimization */
  .heatmap-container {
    overflow-x: auto !important;
    padding: 0.5rem !important;
    margin: 0 -1rem !important;
  }

  .heatmap-container table {
    min-width: 300px !important;
    font-size: 0.8rem !important;
  }

  /* Alerts container mobile */
  .alerts-container {
    max-height: 300px !important;
    overflow-y: auto !important;
  }

  .alert {
    margin-bottom: 0.75rem !important;
    padding: 0.75rem !important;
    border-radius: 8px !important;
    font-size: 0.85rem !important;
  }

  /* Drill-down container mobile */
  .drill-down-container {
    padding: 1rem !important;
    text-align: center !important;
    font-size: 0.9rem !important;
  }
}

@media (max-width: 480px) {
  .chart-card {
    padding: 0.75rem !important;
  }

  .chart-card h3 {
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  .chart-wrapper {
    height: 200px !important;
  }

  .alert {
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
  }
}

/* ===== STEP 9: DATA TABLES MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .table-container {
    overflow-x: auto !important;
    margin: 0 -1rem !important;
    padding: 0 1rem !important;
    border-radius: 8px !important;
  }

  table {
    min-width: 600px !important;
    font-size: 0.8rem !important;
    border-collapse: collapse !important;
  }

  table th {
    padding: 0.5rem 0.3rem !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-align: left !important;
    white-space: nowrap !important;
  }

  table td {
    padding: 0.5rem 0.3rem !important;
    font-size: 0.8rem !important;
    border-bottom: 1px solid var(--border-color) !important;
  }

  /* Responsive table alternative - card layout for very small screens */
  .table-responsive-cards {
    display: none !important;
  }
}

@media (max-width: 480px) {
  /* Hide traditional table and show card layout */
  .table-container table {
    display: none !important;
  }

  .table-responsive-cards {
    display: block !important;
  }

  .table-card {
    background: white !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-bottom: 0.75rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border-left: 4px solid var(--primary-color) !important;
  }

  .table-card-header {
    font-weight: 600 !important;
    color: var(--primary-color) !important;
    margin-bottom: 0.5rem !important;
    font-size: 0.9rem !important;
  }

  .table-card-content {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0.5rem !important;
    font-size: 0.8rem !important;
  }

  .table-card-item {
    display: flex !important;
    flex-direction: column !important;
  }

  .table-card-label {
    font-weight: 500 !important;
    color: var(--text-secondary) !important;
    font-size: 0.7rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin-bottom: 0.2rem !important;
  }

  .table-card-value {
    color: var(--text-primary) !important;
    font-weight: 500 !important;
  }
}

/* ===== STEP 10: PAGINATION MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column !important;
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem !important;
    text-align: center !important;
  }

  .pagination-info {
    font-size: 0.85rem !important;
    color: var(--text-secondary) !important;
    margin-bottom: 0.5rem !important;
  }

  .pagination-controls {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  .pagination-controls button {
    min-width: 44px !important;
    min-height: 44px !important;
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    border-radius: 8px !important;
    border: 1px solid var(--border-color) !important;
    background: white !important;
    color: var(--text-primary) !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .pagination-controls button:hover {
    background: var(--light-bg) !important;
    border-color: var(--primary-color) !important;
  }

  .pagination-controls button.active {
    background: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
  }

  .pagination-controls button:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
  }
}

@media (max-width: 480px) {
  .pagination-controls button {
    min-width: 40px !important;
    min-height: 40px !important;
    font-size: 0.75rem !important;
    padding: 0.4rem !important;
  }
}

/* ===== STEP 11: FORMS MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .form-container,
  .modal-content {
    padding: 1rem !important;
    margin: 0.5rem !important;
    border-radius: 12px !important;
    max-width: 100% !important;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  .form-label {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    color: var(--text-primary) !important;
  }

  .form-control,
  .form-input,
  input,
  select,
  textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    border: 2px solid var(--border-color) !important;
    border-radius: 8px !important;
    background: white !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    box-sizing: border-box !important;
  }

  .form-control:focus,
  .form-input:focus,
  input:focus,
  select:focus,
  textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
  }

  .btn,
  .form-btn {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    margin-top: 0.5rem !important;
  }

  .btn-primary {
    background: var(--primary-color) !important;
    color: white !important;
  }

  .btn-secondary {
    background: var(--text-secondary) !important;
    color: white !important;
  }

  .btn-success {
    background: var(--success-color) !important;
    color: white !important;
  }

  .btn-danger {
    background: var(--danger-color) !important;
    color: white !important;
  }

  .btn:active {
    transform: scale(0.98) !important;
  }

  /* Form rows and columns */
  .form-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .form-col {
    width: 100% !important;
    flex: none !important;
  }

  /* Select dropdowns */
  select {
    appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 1rem !important;
    padding-right: 2.5rem !important;
  }

  /* Checkboxes and radio buttons */
  .form-check {
    margin-bottom: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .form-check-input {
    width: 1.2rem !important;
    height: 1.2rem !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
  }

  .form-check-label {
    font-size: 0.9rem !important;
    margin: 0 !important;
    line-height: 1.4 !important;
  }
}

@media (max-width: 480px) {
  .form-container,
  .modal-content {
    padding: 0.75rem !important;
    margin: 0.25rem !important;
  }

  .form-control,
  .form-input,
  input,
  select,
  textarea {
    padding: 0.6rem !important;
    font-size: 0.85rem !important;
  }

  .btn,
  .form-btn {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.85rem !important;
  }

  .form-label {
    font-size: 0.8rem !important;
  }
}

/* ===== STEP 12: MODALS MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .modal {
    padding: 1rem !important;
    align-items: flex-start !important;
    padding-top: 2rem !important;
  }

  .modal-dialog {
    width: 100% !important;
    max-width: 95% !important;
    margin: 0 auto !important;
  }

  .modal-content {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  }

  .modal-header {
    padding: 1rem !important;
    border-bottom: 1px solid var(--border-color) !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }

  .modal-title {
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem !important;
  }

  .modal-body {
    padding: 1rem !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
  }

  .modal-footer {
    padding: 1rem !important;
    border-top: 1px solid var(--border-color) !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .modal-footer .btn {
    width: 100% !important;
    margin: 0 !important;
  }

  .close {
    position: absolute !important;
    top: 1rem !important;
    right: 1rem !important;
    font-size: 1.5rem !important;
    background: none !important;
    border: none !important;
    color: var(--text-secondary) !important;
    cursor: pointer !important;
    padding: 0.25rem !important;
    line-height: 1 !important;
    min-width: 44px !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

@media (max-width: 480px) {
  .modal {
    padding: 0.5rem !important;
    padding-top: 1rem !important;
  }

  .modal-dialog {
    max-width: 98% !important;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 0.75rem !important;
  }

  .modal-title {
    font-size: 1rem !important;
  }
}

/* ===== STEP 13: ADVANCED FORMS MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* Modern Form Containers */
  .modern-form,
  .form-container,
  .card-body {
    padding: 1rem !important;
    margin: 0.5rem 0 !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* Form Groups and Labels */
  .form-group,
  .modern-form .form-group {
    margin-bottom: 1rem !important;
    width: 100% !important;
  }

  .form-label,
  .modern-form .form-label {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    color: var(--text-primary) !important;
  }

  /* Form Inputs - Enhanced for Touch */
  .form-input,
  .form-control,
  .form-select,
  .modern-form input,
  .modern-form select,
  .modern-form textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important; /* Prevents zoom on iOS */
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    background: white !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    box-sizing: border-box !important;
    -webkit-appearance: none !important;
    appearance: none !important;
  }

  /* Focus States for Better UX */
  .form-input:focus,
  .form-control:focus,
  .form-select:focus,
  .modern-form input:focus,
  .modern-form select:focus,
  .modern-form textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
    background: #fafbfc !important;
  }

  /* Select Dropdowns with Custom Arrow */
  .form-select,
  .modern-form select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 1rem !important;
    padding-right: 2.5rem !important;
  }

  /* Textarea Specific */
  .form-textarea,
  .modern-form textarea {
    min-height: 100px !important;
    resize: vertical !important;
  }

  /* Form Grid System */
  .modern-grid,
  .form-row {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    width: 100% !important;
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .form-col {
    width: 100% !important;
    flex: none !important;
  }

  /* Form Buttons */
  .modern-btn,
  .form-btn,
  .btn {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    margin-top: 0.5rem !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  /* Button Variants */
  .btn-primary,
  .modern-btn.btn-primary {
    background: var(--primary-color) !important;
    color: white !important;
  }

  .btn-secondary,
  .modern-btn.btn-secondary {
    background: #6b7280 !important;
    color: white !important;
  }

  .btn-success,
  .modern-btn.btn-success {
    background: var(--success-color) !important;
    color: white !important;
  }

  .btn-outline,
  .modern-btn.btn-outline {
    background: transparent !important;
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color) !important;
  }

  /* Button Active States */
  .modern-btn:active,
  .form-btn:active,
  .btn:active {
    transform: scale(0.98) !important;
  }

  /* Checkboxes and Radio Buttons */
  .form-check,
  .checkbox-group {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    margin-bottom: 1rem !important;
    padding: 0.5rem !important;
  }

  .form-check-input,
  .checkbox-input,
  .radio-input {
    width: 1.25rem !important;
    height: 1.25rem !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    cursor: pointer !important;
  }

  .form-check-label,
  .checkbox-label,
  .radio-label {
    font-size: 0.9rem !important;
    margin: 0 !important;
    line-height: 1.4 !important;
    cursor: pointer !important;
    flex: 1 !important;
  }
}

@media (max-width: 480px) {
  .modern-form,
  .form-container,
  .card-body {
    padding: 0.75rem !important;
    margin: 0.25rem 0 !important;
  }

  .form-input,
  .form-control,
  .form-select,
  .modern-form input,
  .modern-form select,
  .modern-form textarea {
    padding: 0.6rem !important;
    font-size: 0.9rem !important;
  }

  .modern-btn,
  .form-btn,
  .btn {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.9rem !important;
  }

  .form-label,
  .modern-form .form-label {
    font-size: 0.8rem !important;
  }
}

/* ===== STEP 14: FILE UPLOAD MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* File Upload Containers */
  .file-upload-container,
  .upload-section {
    padding: 1rem !important;
    margin: 1rem 0 !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 2px dashed #e5e7eb !important;
    transition: all 0.3s ease !important;
  }

  .file-upload-container:hover,
  .upload-section:hover {
    border-color: var(--primary-color) !important;
    background: #f8fafc !important;
  }

  /* File Input Styling */
  .file-input,
  input[type="file"] {
    width: 100% !important;
    padding: 1rem !important;
    font-size: 1rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    background: white !important;
    cursor: pointer !important;
    min-height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  /* Custom File Upload Button */
  .file-upload-btn {
    width: 100% !important;
    padding: 1rem !important;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      #5a67d8 100%
    ) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    min-height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  .file-upload-btn:hover {
    background: linear-gradient(
      135deg,
      #5a67d8 0%,
      var(--primary-color) 100%
    ) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
  }

  .file-upload-btn:active {
    transform: translateY(0) scale(0.98) !important;
  }

  /* File Upload Icon */
  .file-upload-icon {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    color: var(--primary-color) !important;
  }

  /* File Upload Text */
  .file-upload-text {
    font-size: 0.9rem !important;
    color: #6b7280 !important;
    text-align: center !important;
    margin-top: 0.5rem !important;
  }

  /* Drag and Drop Area */
  .drag-drop-area {
    width: 100% !important;
    min-height: 120px !important;
    border: 2px dashed #d1d5db !important;
    border-radius: 12px !important;
    background: #f9fafb !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 2rem 1rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
  }

  .drag-drop-area:hover,
  .drag-drop-area.drag-over {
    border-color: var(--primary-color) !important;
    background: #f0f9ff !important;
    transform: scale(1.02) !important;
  }

  .drag-drop-area.drag-over {
    border-color: var(--success-color) !important;
    background: #f0fdf4 !important;
  }

  /* File List Display */
  .file-list {
    margin-top: 1rem !important;
    padding: 0 !important;
    list-style: none !important;
  }

  .file-item {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
    border: 1px solid #e5e7eb !important;
  }

  .file-item-info {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    flex: 1 !important;
  }

  .file-item-icon {
    font-size: 1.2rem !important;
    color: var(--primary-color) !important;
  }

  .file-item-name {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    word-break: break-all !important;
  }

  .file-item-size {
    font-size: 0.8rem !important;
    color: #6b7280 !important;
  }

  .file-item-remove {
    background: #ef4444 !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
  }

  .file-item-remove:hover {
    background: #dc2626 !important;
    transform: scale(1.1) !important;
  }

  /* Progress Bar for Uploads */
  .upload-progress {
    width: 100% !important;
    height: 8px !important;
    background: #e5e7eb !important;
    border-radius: 4px !important;
    overflow: hidden !important;
    margin-top: 0.5rem !important;
  }

  .upload-progress-bar {
    height: 100% !important;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--success-color)
    ) !important;
    border-radius: 4px !important;
    transition: width 0.3s ease !important;
  }

  /* Upload Status Messages */
  .upload-status {
    padding: 0.75rem !important;
    border-radius: 8px !important;
    margin-top: 1rem !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
  }

  .upload-status.success {
    background: #dcfce7 !important;
    color: #166534 !important;
    border: 1px solid #bbf7d0 !important;
  }

  .upload-status.error {
    background: #fef2f2 !important;
    color: #dc2626 !important;
    border: 1px solid #fecaca !important;
  }

  .upload-status.warning {
    background: #fefce8 !important;
    color: #ca8a04 !important;
    border: 1px solid #fde68a !important;
  }

  /* File Type Restrictions */
  .file-restrictions {
    font-size: 0.8rem !important;
    color: #6b7280 !important;
    text-align: center !important;
    margin-top: 0.5rem !important;
    padding: 0.5rem !important;
    background: #f9fafb !important;
    border-radius: 6px !important;
  }
}

@media (max-width: 480px) {
  .file-upload-container,
  .upload-section {
    padding: 0.75rem !important;
    margin: 0.5rem 0 !important;
  }

  .file-upload-btn {
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    min-height: 50px !important;
  }

  .drag-drop-area {
    min-height: 100px !important;
    padding: 1.5rem 0.75rem !important;
  }

  .file-item {
    padding: 0.5rem !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.5rem !important;
  }

  .file-item-info {
    width: 100% !important;
  }

  .file-item-remove {
    align-self: flex-end !important;
  }
}

/* ===== STEP 15: MARKS UPLOAD TABLE MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* Table Wrapper Mobile Optimization */
  .table-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    margin: 0 -1rem !important;
    padding: 0 1rem !important;
    border-radius: 8px !important;
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* Table Mobile Styling */
  .table-wrapper table {
    min-width: 800px !important;
    font-size: 0.85rem !important;
    border-collapse: collapse !important;
    width: 100% !important;
  }

  .table-wrapper th {
    padding: 0.75rem 0.5rem !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    text-align: center !important;
    white-space: nowrap !important;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      #5a67d8 100%
    ) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
  }

  .table-wrapper td {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.8rem !important;
    border: 1px solid #e5e7eb !important;
    text-align: center !important;
    vertical-align: middle !important;
  }

  /* Student Name Column - Fixed Width */
  .table-wrapper td:first-child,
  .table-wrapper th:first-child {
    position: sticky !important;
    left: 0 !important;
    background: white !important;
    z-index: 5 !important;
    min-width: 120px !important;
    max-width: 150px !important;
    font-weight: 600 !important;
    text-align: left !important;
    padding-left: 0.75rem !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
  }

  .table-wrapper th:first-child {
    background: var(--primary-color) !important;
    color: white !important;
    z-index: 15 !important;
  }

  /* Input Fields in Table */
  .table-wrapper input[type="number"],
  .student-mark,
  .component-mark {
    width: 100% !important;
    min-width: 60px !important;
    max-width: 80px !important;
    height: 40px !important;
    padding: 0.5rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 6px !important;
    background: white !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    text-align: center !important;
    transition: all 0.2s ease !important;
    box-sizing: border-box !important;
  }

  .table-wrapper input[type="number"]:focus,
  .student-mark:focus,
  .component-mark:focus {
    border-color: var(--primary-color) !important;
    background: #f0f9ff !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
    transform: scale(1.05) !important;
  }

  /* Subject Columns */
  .table-wrapper td[data-subject-id],
  .table-wrapper th[data-subject-id] {
    min-width: 80px !important;
    max-width: 100px !important;
  }

  /* Last Column (Creative Arts) Special Styling */
  .table-wrapper td:last-child,
  .table-wrapper th:last-child {
    background: #fef3c7 !important;
    border-left: 3px solid #f59e0b !important;
    min-width: 90px !important;
  }

  .table-wrapper td:last-child input {
    background: #fffbeb !important;
    border-color: #f59e0b !important;
  }

  .table-wrapper td:last-child input:focus {
    border-color: #d97706 !important;
    background: #fef3c7 !important;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1) !important;
  }

  /* Table Actions */
  .table-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1rem !important;
    padding: 1rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
  }

  .table-actions .btn {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
  }

  /* Scroll Indicators */
  .table-scroll-indicator {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: rgba(102, 126, 234, 0.9) !important;
    color: white !important;
    padding: 0.5rem !important;
    border-radius: 50% !important;
    font-size: 1.2rem !important;
    z-index: 20 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .table-scroll-indicator.left {
    left: 10px !important;
  }

  .table-scroll-indicator.right {
    right: 10px !important;
  }

  .table-scroll-indicator:hover {
    background: var(--primary-color) !important;
    transform: translateY(-50%) scale(1.1) !important;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    margin: 0 -0.75rem !important;
    padding: 0 0.75rem !important;
  }

  .table-wrapper table {
    min-width: 600px !important;
    font-size: 0.8rem !important;
  }

  .table-wrapper th {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.75rem !important;
  }

  .table-wrapper td {
    padding: 0.25rem 0.15rem !important;
    font-size: 0.75rem !important;
  }

  .table-wrapper input[type="number"],
  .student-mark,
  .component-mark {
    min-width: 50px !important;
    max-width: 65px !important;
    height: 35px !important;
    padding: 0.25rem !important;
    font-size: 0.8rem !important;
  }

  .table-wrapper td:first-child,
  .table-wrapper th:first-child {
    min-width: 100px !important;
    max-width: 120px !important;
    padding-left: 0.5rem !important;
  }

  .table-actions {
    padding: 0.75rem !important;
    gap: 0.5rem !important;
  }

  .table-actions .btn {
    padding: 0.6rem !important;
    font-size: 0.85rem !important;
  }
}

/* ===== STEP 16: ADVANCED CHARTS & ANALYTICS MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* Analytics Page Layout */
  .analytics-page-header {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
  }

  .analytics-page-header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .analytics-page-header p {
    font-size: 0.9rem !important;
    margin: 0 !important;
  }

  /* Analytics Grid System */
  .analytics-main-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    padding: 0 !important;
  }

  .analytics-component {
    background: white !important;
    border-radius: 12px !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e5e7eb !important;
  }

  .analytics-full-width {
    width: 100% !important;
    grid-column: 1 / -1 !important;
  }

  /* Component Headers */
  .component-header {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-bottom: 1rem !important;
    padding-bottom: 0.75rem !important;
    border-bottom: 2px solid #f3f4f6 !important;
  }

  .component-header h3 {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: var(--primary-color) !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .component-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
    justify-content: flex-start !important;
  }

  .component-actions .modern-btn {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.8rem !important;
    border-radius: 6px !important;
    flex: none !important;
  }

  /* Chart Containers */
  .chart-container {
    position: relative !important;
    width: 100% !important;
    height: 300px !important;
    margin: 1rem 0 !important;
    background: #fafbfc !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    box-sizing: border-box !important;
  }

  .chart-wrapper {
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Chart Canvas Mobile Optimization */
  .chart-canvas {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
  }

  /* Chart Legend Mobile */
  .chart-legend {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
    justify-content: center !important;
    margin-top: 1rem !important;
    padding: 0.75rem !important;
    background: #f8fafc !important;
    border-radius: 6px !important;
  }

  .legend-item {
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
    font-size: 0.8rem !important;
    padding: 0.25rem 0.5rem !important;
    background: white !important;
    border-radius: 4px !important;
    border: 1px solid #e5e7eb !important;
  }

  .legend-color {
    width: 12px !important;
    height: 12px !important;
    border-radius: 2px !important;
    flex-shrink: 0 !important;
  }

  /* Analytics Tabs Mobile */
  .analytics-tabs {
    display: flex !important;
    overflow-x: auto !important;
    gap: 0.5rem !important;
    padding: 0.5rem !important;
    margin-bottom: 1rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .analytics-tab {
    flex: none !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.85rem !important;
    font-weight: 500 !important;
    background: white !important;
    color: #6b7280 !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
    min-width: 100px !important;
    text-align: center !important;
  }

  .analytics-tab.active {
    background: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
  }

  .analytics-tab:hover:not(.active) {
    background: #f3f4f6 !important;
    border-color: #d1d5db !important;
  }

  /* Tab Content */
  .tab-content {
    display: none !important;
  }

  .tab-content.active {
    display: block !important;
  }

  /* Analytics Cards */
  .analytics-card {
    background: white !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    border: 1px solid #e5e7eb !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  .analytics-card-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 0.75rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
  }

  .analytics-card-title {
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 !important;
  }

  .analytics-card-value {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin: 0.5rem 0 !important;
  }

  .analytics-card-change {
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
  }

  .change-positive {
    color: var(--success-color) !important;
  }

  .change-negative {
    color: var(--danger-color) !important;
  }

  .change-neutral {
    color: #6b7280 !important;
  }
}

@media (max-width: 480px) {
  .analytics-component {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  .component-header {
    gap: 0.5rem !important;
    margin-bottom: 0.75rem !important;
  }

  .component-header h3 {
    font-size: 1rem !important;
  }

  .chart-container {
    height: 250px !important;
    padding: 0.75rem !important;
  }

  .analytics-tabs {
    padding: 0.25rem !important;
    gap: 0.25rem !important;
  }

  .analytics-tab {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.8rem !important;
    min-width: 80px !important;
  }

  .analytics-card {
    padding: 0.75rem !important;
  }

  .analytics-card-value {
    font-size: 1.25rem !important;
  }
}

/* ===== STEP 17: PERFORMANCE METRICS & DATA VISUALIZATION MOBILE ===== */

@media (max-width: 768px) {
  /* Performance Metrics Grid */
  .metrics-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    margin: 1rem 0 !important;
  }

  .metric-card {
    background: white !important;
    border-radius: 12px !important;
    padding: 1rem !important;
    border: 1px solid #e5e7eb !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
  }

  .metric-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  }

  .metric-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 0.75rem !important;
  }

  .metric-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.2rem !important;
    color: white !important;
  }

  .metric-icon.primary {
    background: var(--primary-color) !important;
  }

  .metric-icon.success {
    background: var(--success-color) !important;
  }

  .metric-icon.warning {
    background: #f59e0b !important;
  }

  .metric-icon.danger {
    background: var(--danger-color) !important;
  }

  .metric-value {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: var(--text-primary) !important;
    margin: 0.5rem 0 !important;
    line-height: 1 !important;
  }

  .metric-label {
    font-size: 0.9rem !important;
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    margin-bottom: 0.5rem !important;
  }

  .metric-change {
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
  }

  /* Student Performance Lists */
  .performance-list {
    max-height: 300px !important;
    overflow-y: auto !important;
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
    border-radius: 8px !important;
    background: #f8fafc !important;
  }

  .performance-item {
    display: flex !important;
    align-items: center !important;
    padding: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    background: white !important;
    border-radius: 6px !important;
    border: 1px solid #e5e7eb !important;
    transition: all 0.2s ease !important;
  }

  .performance-item:hover {
    background: #f0f9ff !important;
    border-color: var(--primary-color) !important;
  }

  .performer-rank {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    background: var(--primary-color) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    font-size: 0.8rem !important;
    margin-right: 0.75rem !important;
    flex-shrink: 0 !important;
  }

  .performer-info {
    flex: 1 !important;
    min-width: 0 !important;
  }

  .performer-name {
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .performer-details {
    font-size: 0.8rem !important;
    color: var(--text-secondary) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .performer-score {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin-left: 0.5rem !important;
    flex-shrink: 0 !important;
  }

  /* Subject Performance Analysis */
  .subject-performance-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
    margin: 1rem 0 !important;
  }

  .subject-item {
    display: flex !important;
    align-items: center !important;
    padding: 0.75rem !important;
    background: white !important;
    border-radius: 8px !important;
    border: 1px solid #e5e7eb !important;
    transition: all 0.2s ease !important;
  }

  .subject-item:hover {
    background: #f8fafc !important;
    border-color: var(--primary-color) !important;
  }

  .subject-icon {
    width: 36px !important;
    height: 36px !important;
    border-radius: 6px !important;
    background: var(--primary-color) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1rem !important;
    margin-right: 0.75rem !important;
    flex-shrink: 0 !important;
  }

  .subject-info {
    flex: 1 !important;
    min-width: 0 !important;
  }

  .subject-name {
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
  }

  .subject-details {
    font-size: 0.8rem !important;
    color: var(--text-secondary) !important;
  }

  .subject-score {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    margin-left: 0.5rem !important;
    flex-shrink: 0 !important;
  }

  .subject-trend {
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    margin-left: 0.5rem !important;
    flex-shrink: 0 !important;
  }

  .trend-excellent {
    color: var(--success-color) !important;
  }

  .trend-good {
    color: #3b82f6 !important;
  }

  .trend-needs-improvement {
    color: #f59e0b !important;
  }

  .trend-poor {
    color: var(--danger-color) !important;
  }

  /* Progress Bars */
  .progress-container {
    width: 100% !important;
    height: 8px !important;
    background: #e5e7eb !important;
    border-radius: 4px !important;
    overflow: hidden !important;
    margin: 0.5rem 0 !important;
  }

  .progress-bar {
    height: 100% !important;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--success-color)
    ) !important;
    border-radius: 4px !important;
    transition: width 0.3s ease !important;
  }

  .progress-label {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-size: 0.8rem !important;
    color: var(--text-secondary) !important;
    margin-top: 0.25rem !important;
  }
}

@media (max-width: 480px) {
  .metric-card {
    padding: 0.75rem !important;
  }

  .metric-value {
    font-size: 1.5rem !important;
  }

  .metric-icon {
    width: 32px !important;
    height: 32px !important;
    font-size: 1rem !important;
  }

  .performance-item {
    padding: 0.5rem !important;
  }

  .performer-rank {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.75rem !important;
    margin-right: 0.5rem !important;
  }

  .performer-name {
    font-size: 0.85rem !important;
  }

  .performer-details {
    font-size: 0.75rem !important;
  }

  .performer-score {
    font-size: 1rem !important;
  }

  .subject-item {
    padding: 0.5rem !important;
  }

  .subject-icon {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.9rem !important;
    margin-right: 0.5rem !important;
  }
}

/* ===== STEP 18: STUDENT PORTAL MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* Parent/Student Portal Navigation */
  .parent-navbar,
  .student-navbar {
    padding: 0.75rem 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  }

  .parent-navbar .container,
  .student-navbar .container {
    padding: 0 1rem !important;
  }

  .parent-brand,
  .student-brand {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: white !important;
  }

  .parent-brand i,
  .student-brand i {
    font-size: 1.2rem !important;
  }

  .parent-nav-links,
  .student-nav-links {
    display: flex !important;
    gap: 0.5rem !important;
    margin-left: auto !important;
  }

  .parent-nav-link,
  .student-nav-link {
    color: white !important;
    text-decoration: none !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    font-size: 0.85rem !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
  }

  .parent-nav-link:hover,
  .student-nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
  }

  .parent-nav-link.logout,
  .student-nav-link.logout {
    background: rgba(239, 68, 68, 0.2) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
  }

  .parent-nav-link.logout:hover,
  .student-nav-link.logout:hover {
    background: rgba(239, 68, 68, 0.3) !important;
  }

  /* Student Portal Dashboard */
  .student-dashboard-container {
    padding: 1rem !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  .student-welcome-section {
    background: white !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    text-align: center !important;
  }

  .student-welcome-title {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin-bottom: 0.5rem !important;
  }

  .student-welcome-subtitle {
    font-size: 0.9rem !important;
    color: var(--text-secondary) !important;
    margin-bottom: 1rem !important;
  }

  .student-info-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
    margin-top: 1rem !important;
  }

  .student-info-item {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0.75rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
    border: 1px solid #e5e7eb !important;
  }

  .student-info-label {
    font-weight: 500 !important;
    color: var(--text-secondary) !important;
    font-size: 0.85rem !important;
  }

  .student-info-value {
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    font-size: 0.9rem !important;
  }

  /* Parent Portal Dashboard */
  .parent-dashboard-container {
    padding: 1rem !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  .parent-welcome-section {
    background: white !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    text-align: center !important;
  }

  .parent-welcome-title {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin-bottom: 0.5rem !important;
  }

  .parent-welcome-subtitle {
    font-size: 0.9rem !important;
    color: var(--text-secondary) !important;
    margin-bottom: 1rem !important;
  }

  /* Children Cards */
  .children-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    margin: 1.5rem 0 !important;
  }

  .child-card {
    background: white !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e5e7eb !important;
    transition: all 0.2s ease !important;
  }

  .child-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  }

  .child-card-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 1rem !important;
    padding-bottom: 0.75rem !important;
    border-bottom: 2px solid #f3f4f6 !important;
  }

  .child-name {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin: 0 !important;
  }

  .child-class {
    font-size: 0.8rem !important;
    color: var(--text-secondary) !important;
    background: #f0f9ff !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 4px !important;
    border: 1px solid #bfdbfe !important;
  }

  .child-details-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
  }

  .child-detail-item {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0.5rem !important;
    background: #f8fafc !important;
    border-radius: 6px !important;
    font-size: 0.85rem !important;
  }

  .child-detail-label {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
  }

  .child-detail-value {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
  }

  .child-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    margin-top: 1rem !important;
  }

  .child-action-btn {
    width: 100% !important;
    padding: 0.75rem !important;
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    text-decoration: none !important;
  }

  .child-action-btn:hover {
    background: #5a67d8 !important;
    transform: translateY(-1px) !important;
    color: white !important;
  }

  .child-action-btn.secondary {
    background: #6b7280 !important;
  }

  .child-action-btn.secondary:hover {
    background: #4b5563 !important;
  }

  /* Recent Activity Section */
  .recent-activity-section {
    background: white !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    margin-top: 1.5rem !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  .recent-activity-title {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin-bottom: 1rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .activity-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .activity-item {
    display: flex !important;
    align-items: center !important;
    padding: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
    border: 1px solid #e5e7eb !important;
  }

  .activity-icon {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    background: var(--primary-color) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.8rem !important;
    margin-right: 0.75rem !important;
    flex-shrink: 0 !important;
  }

  .activity-content {
    flex: 1 !important;
    min-width: 0 !important;
  }

  .activity-title {
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
  }

  .activity-details {
    font-size: 0.8rem !important;
    color: var(--text-secondary) !important;
  }

  .activity-time {
    font-size: 0.75rem !important;
    color: var(--text-secondary) !important;
    margin-left: 0.5rem !important;
    flex-shrink: 0 !important;
  }
}

@media (max-width: 480px) {
  .student-dashboard-container,
  .parent-dashboard-container {
    padding: 0.75rem !important;
  }

  .student-welcome-section,
  .parent-welcome-section {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .student-welcome-title,
  .parent-welcome-title {
    font-size: 1.1rem !important;
  }

  .child-card {
    padding: 1rem !important;
  }

  .child-name {
    font-size: 1rem !important;
  }

  .child-actions {
    gap: 0.25rem !important;
  }

  .child-action-btn {
    padding: 0.6rem !important;
    font-size: 0.85rem !important;
  }

  .recent-activity-section {
    padding: 1rem !important;
    margin-top: 1rem !important;
  }

  .activity-item {
    padding: 0.5rem !important;
  }

  .activity-icon {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.75rem !important;
    margin-right: 0.5rem !important;
  }
}

/* ===== PWA STYLES ===== */

/* Install Prompt */
.pwa-install-prompt {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 10000;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pwa-install-prompt:not(.hidden) {
  transform: translateY(0);
  opacity: 1;
}

.install-prompt-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.install-prompt-icon {
  font-size: 24px;
  color: white;
}

.install-prompt-text {
  flex: 1;
}

.install-prompt-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.install-prompt-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.install-prompt-actions {
  display: flex;
  gap: 8px;
}

.btn-install {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-install:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-dismiss {
  background: transparent;
  color: white;
  border: none;
  padding: 8px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.btn-dismiss:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

/* Offline Indicator */
.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #f59e0b;
  color: white;
  padding: 12px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  z-index: 10001;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.offline-indicator.show {
  transform: translateY(0);
}

/* Update Notification */
.update-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  animation: slideInRight 0.3s ease;
}

.update-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.update-content i {
  color: #667eea;
  font-size: 18px;
}

.update-content span {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.update-content button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

#update-app-btn {
  background: #667eea;
  color: white;
  border: none;
}

#update-app-btn:hover {
  background: #5a67d8;
}

#dismiss-update-btn {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

#dismiss-update-btn:hover {
  background: #f9fafb;
}

/* Install Success Message */
.install-success-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #10b981;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10000;
  animation: slideInDown 0.3s ease;
}

.success-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* PWA Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

/* PWA Responsive Adjustments */
@media (max-width: 768px) {
  .pwa-install-prompt {
    left: 10px;
    right: 10px;
    bottom: 10px;
  }

  .install-prompt-content {
    padding: 12px;
  }

  .install-prompt-text h3 {
    font-size: 14px;
  }

  .install-prompt-text p {
    font-size: 12px;
  }

  .btn-install {
    padding: 6px 12px;
    font-size: 12px;
  }

  .update-notification {
    top: 10px;
    right: 10px;
    left: 10px;
  }

  .offline-indicator {
    font-size: 12px;
    padding: 10px;
  }
}

/* PWA Standalone Mode Adjustments */
@media (display-mode: standalone) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .navbar {
    padding-top: calc(1rem + env(safe-area-inset-top));
  }

  .pwa-install-prompt {
    display: none !important;
  }
}
