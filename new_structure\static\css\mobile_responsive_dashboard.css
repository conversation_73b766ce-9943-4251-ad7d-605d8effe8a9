/*
 * Mobile Responsive Dashboard Styles for Hillview School Management System
 * Comprehensive mobile-first responsive design for dashboard pages
 */

/* ===== DESKTOP NAVIGATION BAR FIX ===== */

/* Fix horizontal scrolling issue on desktop */
@media (min-width: 769px) {
  .navbar {
    padding: 0.5rem 0 !important;
  }

  .navbar .container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    max-width: 100% !important;
    padding: 0 1rem !important;
  }

  .navbar-brand {
    flex-shrink: 0 !important;
    max-width: 250px !important;
    margin-right: 1rem !important;
  }

  .navbar-brand span {
    font-size: 1rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .navbar-nav {
    display: flex !important;
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    flex: 1 !important;
    justify-content: flex-end !important;
    gap: 0.25rem !important;
    margin: 0 !important;
    padding: 0 !important;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE/Edge */
  }

  .navbar-nav::-webkit-scrollbar {
    display: none !important; /* Chrome/Safari */
  }

  .navbar-nav li {
    flex-shrink: 0 !important;
    white-space: nowrap !important;
  }

  .navbar-nav .nav-link {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.85rem !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
  }

  .navbar-nav .nav-link i {
    font-size: 0.8rem !important;
    margin-right: 0.4rem !important;
  }

  .navbar-nav .logout-btn {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #ef4444 !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
    margin-left: 0.5rem !important;
    font-weight: 600 !important;
  }

  .navbar-nav .logout-btn:hover {
    background: #ef4444 !important;
    color: white !important;
  }
}

/* Medium desktop - more compact navigation */
@media (min-width: 769px) and (max-width: 1199px) {
  .navbar-brand {
    max-width: 200px !important;
  }

  .navbar-brand span {
    font-size: 0.9rem !important;
  }

  .navbar-nav .nav-link {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.8rem !important;
  }

  .navbar-nav .nav-link i {
    font-size: 0.75rem !important;
    margin-right: 0.3rem !important;
  }

  /* Hide text on very compact screens, show only icons */
  @media (max-width: 1024px) {
    .navbar-nav .nav-link span {
      display: none !important;
    }

    .navbar-nav .nav-link {
      padding: 0.5rem !important;
      min-width: 40px !important;
      text-align: center !important;
    }

    .navbar-nav .nav-link i {
      margin-right: 0 !important;
      font-size: 0.9rem !important;
    }
  }
}

/* Large desktop optimization */
@media (min-width: 1200px) {
  .navbar-nav {
    gap: 0.5rem !important;
  }

  .navbar-nav .nav-link {
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem !important;
  }

  .navbar-nav .nav-link i {
    font-size: 0.85rem !important;
    margin-right: 0.5rem !important;
  }
}

/* ===== STEP 1: NAVIGATION BAR MOBILE RESPONSIVENESS ===== */

/* Mobile Navigation Base Styles */
@media (max-width: 768px) {
  .navbar {
    padding: 0.75rem 0 !important;
    position: relative !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  .navbar .container {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding: 0 1rem !important;
  }

  .navbar-brand {
    width: 100% !important;
    margin-bottom: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  .navbar-brand span {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
  }

  .navbar-brand img {
    height: 32px !important;
    margin-right: 8px !important;
  }

  .navbar-nav {
    width: 100% !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .navbar-nav li {
    flex: 1 1 auto !important;
    min-width: 120px !important;
    max-width: 150px !important;
  }

  .navbar-nav .nav-link {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    padding: 0.5rem 0.25rem !important;
    font-size: 0.75rem !important;
    text-align: center !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
    min-height: 60px !important;
    justify-content: center !important;
  }

  .navbar-nav .nav-link i {
    font-size: 1.2rem !important;
    margin-bottom: 0.25rem !important;
    display: block !important;
  }

  .navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px) !important;
  }
}

/* Small Mobile Navigation (480px and below) */
@media (max-width: 480px) {
  .navbar-brand span {
    font-size: 1rem !important;
  }

  .navbar-nav li {
    min-width: 100px !important;
    max-width: 120px !important;
  }

  .navbar-nav .nav-link {
    font-size: 0.7rem !important;
    padding: 0.4rem 0.2rem !important;
    min-height: 55px !important;
  }

  .navbar-nav .nav-link i {
    font-size: 1.1rem !important;
  }
}

/* ===== STEP 2: DASHBOARD CONTAINER MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem 0.75rem !important;
    margin: 0 !important;
    max-width: 100% !important;
  }

  .container {
    padding: 0 0.75rem !important;
    margin: 0 auto !important;
    max-width: 100% !important;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 0.75rem 0.5rem !important;
  }

  .container {
    padding: 0 0.5rem !important;
  }
}

/* ===== STEP 3: DASHBOARD HEADER MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .dashboard-header {
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
    border-radius: 12px !important;
  }

  .header-content {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    gap: 1rem !important;
  }

  .header-info {
    width: 100% !important;
    margin-bottom: 0 !important;
  }

  .dashboard-title {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.2 !important;
  }

  .dashboard-title i {
    font-size: 1.3rem !important;
    margin-right: 0.5rem !important;
  }

  .dashboard-subtitle {
    font-size: 0.9rem !important;
    margin-bottom: 0 !important;
    opacity: 0.8 !important;
  }

  .dashboard-subtitle i {
    font-size: 0.8rem !important;
    margin-right: 0.3rem !important;
  }

  .header-actions {
    width: 100% !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
  }

  .header-actions .action-btn {
    flex: 1 1 auto !important;
    min-width: 120px !important;
    max-width: 150px !important;
    padding: 0.6rem 0.8rem !important;
    font-size: 0.8rem !important;
  }

  .header-actions .action-btn i {
    font-size: 0.9rem !important;
    margin-right: 0.3rem !important;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  .dashboard-title {
    font-size: 1.3rem !important;
  }

  .dashboard-subtitle {
    font-size: 0.8rem !important;
  }

  .header-actions .action-btn {
    min-width: 100px !important;
    max-width: 130px !important;
    padding: 0.5rem 0.6rem !important;
    font-size: 0.75rem !important;
  }
}

/* ===== STEP 4: DASHBOARD ACTIONS MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .dashboard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.75rem !important;
    margin-bottom: 1.5rem !important;
    justify-content: center !important;
  }

  .dashboard-actions .action-btn {
    flex: 1 1 auto !important;
    min-width: 140px !important;
    max-width: 180px !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.85rem !important;
    text-align: center !important;
    border-radius: 10px !important;
  }

  .dashboard-actions .action-btn i {
    font-size: 1rem !important;
    margin-right: 0.5rem !important;
    display: inline-block !important;
  }

  .dashboard-actions .action-btn span {
    display: inline !important;
  }
}

@media (max-width: 480px) {
  .dashboard-actions {
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
  }

  .dashboard-actions .action-btn {
    min-width: 120px !important;
    max-width: 150px !important;
    padding: 0.6rem 0.8rem !important;
    font-size: 0.8rem !important;
  }

  .dashboard-actions .action-btn i {
    font-size: 0.9rem !important;
    margin-right: 0.3rem !important;
  }
}

/* ===== STEP 5: RESPONSIVE GRID SYSTEM ===== */

@media (max-width: 768px) {
  /* Convert multi-column layouts to single column */
  .row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .col,
  .col-md-6,
  .col-lg-4,
  .col-lg-6,
  .col-lg-8,
  .col-xl-3,
  .col-xl-4,
  .col-xl-6,
  .col-xl-8 {
    width: 100% !important;
    flex: none !important;
    margin-bottom: 1rem !important;
  }

  /* Card layouts */
  .card,
  .chart-card,
  .stats-card {
    margin-bottom: 1rem !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }
}

/* ===== STEP 6: TOUCH-FRIENDLY INTERACTIONS ===== */

@media (max-width: 768px) {
  /* Ensure all interactive elements are touch-friendly */
  button,
  .btn,
  .action-btn,
  .nav-link,
  a {
    min-height: 44px !important;
    min-width: 44px !important;
    touch-action: manipulation !important;
  }

  /* Remove hover effects on touch devices */
  .action-btn:hover,
  .nav-link:hover,
  .btn:hover {
    transform: none !important;
  }

  /* Add active states for touch feedback */
  .action-btn:active,
  .nav-link:active,
  .btn:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
}

/* ===== STEP 7: TYPOGRAPHY SCALING ===== */

@media (max-width: 768px) {
  h1 {
    font-size: 1.5rem !important;
  }
  h2 {
    font-size: 1.3rem !important;
  }
  h3 {
    font-size: 1.1rem !important;
  }
  h4 {
    font-size: 1rem !important;
  }
  h5 {
    font-size: 0.9rem !important;
  }
  h6 {
    font-size: 0.8rem !important;
  }

  p,
  span,
  div {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.3rem !important;
  }
  h2 {
    font-size: 1.2rem !important;
  }
  h3 {
    font-size: 1rem !important;
  }
  h4 {
    font-size: 0.95rem !important;
  }
  h5 {
    font-size: 0.85rem !important;
  }
  h6 {
    font-size: 0.8rem !important;
  }

  p,
  span,
  div {
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
  }
}

/* ===== STEP 8: CHARTS CONTAINER MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .charts-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    margin-bottom: 2rem !important;
  }

  .chart-card {
    width: 100% !important;
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    background: white !important;
  }

  .chart-card h3 {
    font-size: 1.1rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
    color: var(--text-primary) !important;
  }

  .chart-wrapper {
    position: relative !important;
    height: 250px !important;
    width: 100% !important;
    margin: 0 auto !important;
  }

  .chart-wrapper canvas {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Heatmap mobile optimization */
  .heatmap-container {
    overflow-x: auto !important;
    padding: 0.5rem !important;
    margin: 0 -1rem !important;
  }

  .heatmap-container table {
    min-width: 300px !important;
    font-size: 0.8rem !important;
  }

  /* Alerts container mobile */
  .alerts-container {
    max-height: 300px !important;
    overflow-y: auto !important;
  }

  .alert {
    margin-bottom: 0.75rem !important;
    padding: 0.75rem !important;
    border-radius: 8px !important;
    font-size: 0.85rem !important;
  }

  /* Drill-down container mobile */
  .drill-down-container {
    padding: 1rem !important;
    text-align: center !important;
    font-size: 0.9rem !important;
  }
}

@media (max-width: 480px) {
  .chart-card {
    padding: 0.75rem !important;
  }

  .chart-card h3 {
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  .chart-wrapper {
    height: 200px !important;
  }

  .alert {
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
  }
}

/* ===== STEP 9: DATA TABLES MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .table-container {
    overflow-x: auto !important;
    margin: 0 -1rem !important;
    padding: 0 1rem !important;
    border-radius: 8px !important;
  }

  table {
    min-width: 600px !important;
    font-size: 0.8rem !important;
    border-collapse: collapse !important;
  }

  table th {
    padding: 0.5rem 0.3rem !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-align: left !important;
    white-space: nowrap !important;
  }

  table td {
    padding: 0.5rem 0.3rem !important;
    font-size: 0.8rem !important;
    border-bottom: 1px solid var(--border-color) !important;
  }

  /* Responsive table alternative - card layout for very small screens */
  .table-responsive-cards {
    display: none !important;
  }
}

@media (max-width: 480px) {
  /* Hide traditional table and show card layout */
  .table-container table {
    display: none !important;
  }

  .table-responsive-cards {
    display: block !important;
  }

  .table-card {
    background: white !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-bottom: 0.75rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border-left: 4px solid var(--primary-color) !important;
  }

  .table-card-header {
    font-weight: 600 !important;
    color: var(--primary-color) !important;
    margin-bottom: 0.5rem !important;
    font-size: 0.9rem !important;
  }

  .table-card-content {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0.5rem !important;
    font-size: 0.8rem !important;
  }

  .table-card-item {
    display: flex !important;
    flex-direction: column !important;
  }

  .table-card-label {
    font-weight: 500 !important;
    color: var(--text-secondary) !important;
    font-size: 0.7rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin-bottom: 0.2rem !important;
  }

  .table-card-value {
    color: var(--text-primary) !important;
    font-weight: 500 !important;
  }
}

/* ===== STEP 10: PAGINATION MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column !important;
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem !important;
    text-align: center !important;
  }

  .pagination-info {
    font-size: 0.85rem !important;
    color: var(--text-secondary) !important;
    margin-bottom: 0.5rem !important;
  }

  .pagination-controls {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  .pagination-controls button {
    min-width: 44px !important;
    min-height: 44px !important;
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    border-radius: 8px !important;
    border: 1px solid var(--border-color) !important;
    background: white !important;
    color: var(--text-primary) !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .pagination-controls button:hover {
    background: var(--light-bg) !important;
    border-color: var(--primary-color) !important;
  }

  .pagination-controls button.active {
    background: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
  }

  .pagination-controls button:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
  }
}

@media (max-width: 480px) {
  .pagination-controls button {
    min-width: 40px !important;
    min-height: 40px !important;
    font-size: 0.75rem !important;
    padding: 0.4rem !important;
  }
}

/* ===== STEP 11: FORMS MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .form-container,
  .modal-content {
    padding: 1rem !important;
    margin: 0.5rem !important;
    border-radius: 12px !important;
    max-width: 100% !important;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  .form-label {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    color: var(--text-primary) !important;
  }

  .form-control,
  .form-input,
  input,
  select,
  textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    border: 2px solid var(--border-color) !important;
    border-radius: 8px !important;
    background: white !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    box-sizing: border-box !important;
  }

  .form-control:focus,
  .form-input:focus,
  input:focus,
  select:focus,
  textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
  }

  .btn,
  .form-btn {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    margin-top: 0.5rem !important;
  }

  .btn-primary {
    background: var(--primary-color) !important;
    color: white !important;
  }

  .btn-secondary {
    background: var(--text-secondary) !important;
    color: white !important;
  }

  .btn-success {
    background: var(--success-color) !important;
    color: white !important;
  }

  .btn-danger {
    background: var(--danger-color) !important;
    color: white !important;
  }

  .btn:active {
    transform: scale(0.98) !important;
  }

  /* Form rows and columns */
  .form-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .form-col {
    width: 100% !important;
    flex: none !important;
  }

  /* Select dropdowns */
  select {
    appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 1rem !important;
    padding-right: 2.5rem !important;
  }

  /* Checkboxes and radio buttons */
  .form-check {
    margin-bottom: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .form-check-input {
    width: 1.2rem !important;
    height: 1.2rem !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
  }

  .form-check-label {
    font-size: 0.9rem !important;
    margin: 0 !important;
    line-height: 1.4 !important;
  }
}

@media (max-width: 480px) {
  .form-container,
  .modal-content {
    padding: 0.75rem !important;
    margin: 0.25rem !important;
  }

  .form-control,
  .form-input,
  input,
  select,
  textarea {
    padding: 0.6rem !important;
    font-size: 0.85rem !important;
  }

  .btn,
  .form-btn {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.85rem !important;
  }

  .form-label {
    font-size: 0.8rem !important;
  }
}

/* ===== STEP 12: MODALS MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .modal {
    padding: 1rem !important;
    align-items: flex-start !important;
    padding-top: 2rem !important;
  }

  .modal-dialog {
    width: 100% !important;
    max-width: 95% !important;
    margin: 0 auto !important;
  }

  .modal-content {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  }

  .modal-header {
    padding: 1rem !important;
    border-bottom: 1px solid var(--border-color) !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }

  .modal-title {
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem !important;
  }

  .modal-body {
    padding: 1rem !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
  }

  .modal-footer {
    padding: 1rem !important;
    border-top: 1px solid var(--border-color) !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .modal-footer .btn {
    width: 100% !important;
    margin: 0 !important;
  }

  .close {
    position: absolute !important;
    top: 1rem !important;
    right: 1rem !important;
    font-size: 1.5rem !important;
    background: none !important;
    border: none !important;
    color: var(--text-secondary) !important;
    cursor: pointer !important;
    padding: 0.25rem !important;
    line-height: 1 !important;
    min-width: 44px !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

@media (max-width: 480px) {
  .modal {
    padding: 0.5rem !important;
    padding-top: 1rem !important;
  }

  .modal-dialog {
    max-width: 98% !important;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 0.75rem !important;
  }

  .modal-title {
    font-size: 1rem !important;
  }
}

/* ===== STEP 13: ADVANCED FORMS MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* Modern Form Containers */
  .modern-form,
  .form-container,
  .card-body {
    padding: 1rem !important;
    margin: 0.5rem 0 !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* Form Groups and Labels */
  .form-group,
  .modern-form .form-group {
    margin-bottom: 1rem !important;
    width: 100% !important;
  }

  .form-label,
  .modern-form .form-label {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    color: var(--text-primary) !important;
  }

  /* Form Inputs - Enhanced for Touch */
  .form-input,
  .form-control,
  .form-select,
  .modern-form input,
  .modern-form select,
  .modern-form textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important; /* Prevents zoom on iOS */
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    background: white !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    box-sizing: border-box !important;
    -webkit-appearance: none !important;
    appearance: none !important;
  }

  /* Focus States for Better UX */
  .form-input:focus,
  .form-control:focus,
  .form-select:focus,
  .modern-form input:focus,
  .modern-form select:focus,
  .modern-form textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
    background: #fafbfc !important;
  }

  /* Select Dropdowns with Custom Arrow */
  .form-select,
  .modern-form select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 1rem !important;
    padding-right: 2.5rem !important;
  }

  /* Textarea Specific */
  .form-textarea,
  .modern-form textarea {
    min-height: 100px !important;
    resize: vertical !important;
  }

  /* Form Grid System */
  .modern-grid,
  .form-row {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    width: 100% !important;
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .form-col {
    width: 100% !important;
    flex: none !important;
  }

  /* Form Buttons */
  .modern-btn,
  .form-btn,
  .btn {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-height: 44px !important;
    margin-top: 0.5rem !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  /* Button Variants */
  .btn-primary,
  .modern-btn.btn-primary {
    background: var(--primary-color) !important;
    color: white !important;
  }

  .btn-secondary,
  .modern-btn.btn-secondary {
    background: #6b7280 !important;
    color: white !important;
  }

  .btn-success,
  .modern-btn.btn-success {
    background: var(--success-color) !important;
    color: white !important;
  }

  .btn-outline,
  .modern-btn.btn-outline {
    background: transparent !important;
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color) !important;
  }

  /* Button Active States */
  .modern-btn:active,
  .form-btn:active,
  .btn:active {
    transform: scale(0.98) !important;
  }

  /* Checkboxes and Radio Buttons */
  .form-check,
  .checkbox-group {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    margin-bottom: 1rem !important;
    padding: 0.5rem !important;
  }

  .form-check-input,
  .checkbox-input,
  .radio-input {
    width: 1.25rem !important;
    height: 1.25rem !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    cursor: pointer !important;
  }

  .form-check-label,
  .checkbox-label,
  .radio-label {
    font-size: 0.9rem !important;
    margin: 0 !important;
    line-height: 1.4 !important;
    cursor: pointer !important;
    flex: 1 !important;
  }
}

@media (max-width: 480px) {
  .modern-form,
  .form-container,
  .card-body {
    padding: 0.75rem !important;
    margin: 0.25rem 0 !important;
  }

  .form-input,
  .form-control,
  .form-select,
  .modern-form input,
  .modern-form select,
  .modern-form textarea {
    padding: 0.6rem !important;
    font-size: 0.9rem !important;
  }

  .modern-btn,
  .form-btn,
  .btn {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.9rem !important;
  }

  .form-label,
  .modern-form .form-label {
    font-size: 0.8rem !important;
  }
}

/* ===== STEP 14: FILE UPLOAD MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* File Upload Containers */
  .file-upload-container,
  .upload-section {
    padding: 1rem !important;
    margin: 1rem 0 !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 2px dashed #e5e7eb !important;
    transition: all 0.3s ease !important;
  }

  .file-upload-container:hover,
  .upload-section:hover {
    border-color: var(--primary-color) !important;
    background: #f8fafc !important;
  }

  /* File Input Styling */
  .file-input,
  input[type="file"] {
    width: 100% !important;
    padding: 1rem !important;
    font-size: 1rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    background: white !important;
    cursor: pointer !important;
    min-height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  /* Custom File Upload Button */
  .file-upload-btn {
    width: 100% !important;
    padding: 1rem !important;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      #5a67d8 100%
    ) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    min-height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  .file-upload-btn:hover {
    background: linear-gradient(
      135deg,
      #5a67d8 0%,
      var(--primary-color) 100%
    ) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
  }

  .file-upload-btn:active {
    transform: translateY(0) scale(0.98) !important;
  }

  /* File Upload Icon */
  .file-upload-icon {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    color: var(--primary-color) !important;
  }

  /* File Upload Text */
  .file-upload-text {
    font-size: 0.9rem !important;
    color: #6b7280 !important;
    text-align: center !important;
    margin-top: 0.5rem !important;
  }

  /* Drag and Drop Area */
  .drag-drop-area {
    width: 100% !important;
    min-height: 120px !important;
    border: 2px dashed #d1d5db !important;
    border-radius: 12px !important;
    background: #f9fafb !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 2rem 1rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
  }

  .drag-drop-area:hover,
  .drag-drop-area.drag-over {
    border-color: var(--primary-color) !important;
    background: #f0f9ff !important;
    transform: scale(1.02) !important;
  }

  .drag-drop-area.drag-over {
    border-color: var(--success-color) !important;
    background: #f0fdf4 !important;
  }

  /* File List Display */
  .file-list {
    margin-top: 1rem !important;
    padding: 0 !important;
    list-style: none !important;
  }

  .file-item {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
    border: 1px solid #e5e7eb !important;
  }

  .file-item-info {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    flex: 1 !important;
  }

  .file-item-icon {
    font-size: 1.2rem !important;
    color: var(--primary-color) !important;
  }

  .file-item-name {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    word-break: break-all !important;
  }

  .file-item-size {
    font-size: 0.8rem !important;
    color: #6b7280 !important;
  }

  .file-item-remove {
    background: #ef4444 !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
  }

  .file-item-remove:hover {
    background: #dc2626 !important;
    transform: scale(1.1) !important;
  }

  /* Progress Bar for Uploads */
  .upload-progress {
    width: 100% !important;
    height: 8px !important;
    background: #e5e7eb !important;
    border-radius: 4px !important;
    overflow: hidden !important;
    margin-top: 0.5rem !important;
  }

  .upload-progress-bar {
    height: 100% !important;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--success-color)
    ) !important;
    border-radius: 4px !important;
    transition: width 0.3s ease !important;
  }

  /* Upload Status Messages */
  .upload-status {
    padding: 0.75rem !important;
    border-radius: 8px !important;
    margin-top: 1rem !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
  }

  .upload-status.success {
    background: #dcfce7 !important;
    color: #166534 !important;
    border: 1px solid #bbf7d0 !important;
  }

  .upload-status.error {
    background: #fef2f2 !important;
    color: #dc2626 !important;
    border: 1px solid #fecaca !important;
  }

  .upload-status.warning {
    background: #fefce8 !important;
    color: #ca8a04 !important;
    border: 1px solid #fde68a !important;
  }

  /* File Type Restrictions */
  .file-restrictions {
    font-size: 0.8rem !important;
    color: #6b7280 !important;
    text-align: center !important;
    margin-top: 0.5rem !important;
    padding: 0.5rem !important;
    background: #f9fafb !important;
    border-radius: 6px !important;
  }
}

@media (max-width: 480px) {
  .file-upload-container,
  .upload-section {
    padding: 0.75rem !important;
    margin: 0.5rem 0 !important;
  }

  .file-upload-btn {
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    min-height: 50px !important;
  }

  .drag-drop-area {
    min-height: 100px !important;
    padding: 1.5rem 0.75rem !important;
  }

  .file-item {
    padding: 0.5rem !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.5rem !important;
  }

  .file-item-info {
    width: 100% !important;
  }

  .file-item-remove {
    align-self: flex-end !important;
  }
}

/* ===== STEP 15: MARKS UPLOAD TABLE MOBILE OPTIMIZATION ===== */

@media (max-width: 768px) {
  /* Table Wrapper Mobile Optimization */
  .table-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    margin: 0 -1rem !important;
    padding: 0 1rem !important;
    border-radius: 8px !important;
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* Table Mobile Styling */
  .table-wrapper table {
    min-width: 800px !important;
    font-size: 0.85rem !important;
    border-collapse: collapse !important;
    width: 100% !important;
  }

  .table-wrapper th {
    padding: 0.75rem 0.5rem !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    text-align: center !important;
    white-space: nowrap !important;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      #5a67d8 100%
    ) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
  }

  .table-wrapper td {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.8rem !important;
    border: 1px solid #e5e7eb !important;
    text-align: center !important;
    vertical-align: middle !important;
  }

  /* Student Name Column - Fixed Width */
  .table-wrapper td:first-child,
  .table-wrapper th:first-child {
    position: sticky !important;
    left: 0 !important;
    background: white !important;
    z-index: 5 !important;
    min-width: 120px !important;
    max-width: 150px !important;
    font-weight: 600 !important;
    text-align: left !important;
    padding-left: 0.75rem !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
  }

  .table-wrapper th:first-child {
    background: var(--primary-color) !important;
    color: white !important;
    z-index: 15 !important;
  }

  /* Input Fields in Table */
  .table-wrapper input[type="number"],
  .student-mark,
  .component-mark {
    width: 100% !important;
    min-width: 60px !important;
    max-width: 80px !important;
    height: 40px !important;
    padding: 0.5rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 6px !important;
    background: white !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    text-align: center !important;
    transition: all 0.2s ease !important;
    box-sizing: border-box !important;
  }

  .table-wrapper input[type="number"]:focus,
  .student-mark:focus,
  .component-mark:focus {
    border-color: var(--primary-color) !important;
    background: #f0f9ff !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
    transform: scale(1.05) !important;
  }

  /* Subject Columns */
  .table-wrapper td[data-subject-id],
  .table-wrapper th[data-subject-id] {
    min-width: 80px !important;
    max-width: 100px !important;
  }

  /* Last Column (Creative Arts) Special Styling */
  .table-wrapper td:last-child,
  .table-wrapper th:last-child {
    background: #fef3c7 !important;
    border-left: 3px solid #f59e0b !important;
    min-width: 90px !important;
  }

  .table-wrapper td:last-child input {
    background: #fffbeb !important;
    border-color: #f59e0b !important;
  }

  .table-wrapper td:last-child input:focus {
    border-color: #d97706 !important;
    background: #fef3c7 !important;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1) !important;
  }

  /* Table Actions */
  .table-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1rem !important;
    padding: 1rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
  }

  .table-actions .btn {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
  }

  /* Scroll Indicators */
  .table-scroll-indicator {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: rgba(102, 126, 234, 0.9) !important;
    color: white !important;
    padding: 0.5rem !important;
    border-radius: 50% !important;
    font-size: 1.2rem !important;
    z-index: 20 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .table-scroll-indicator.left {
    left: 10px !important;
  }

  .table-scroll-indicator.right {
    right: 10px !important;
  }

  .table-scroll-indicator:hover {
    background: var(--primary-color) !important;
    transform: translateY(-50%) scale(1.1) !important;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    margin: 0 -0.75rem !important;
    padding: 0 0.75rem !important;
  }

  .table-wrapper table {
    min-width: 600px !important;
    font-size: 0.8rem !important;
  }

  .table-wrapper th {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.75rem !important;
  }

  .table-wrapper td {
    padding: 0.25rem 0.15rem !important;
    font-size: 0.75rem !important;
  }

  .table-wrapper input[type="number"],
  .student-mark,
  .component-mark {
    min-width: 50px !important;
    max-width: 65px !important;
    height: 35px !important;
    padding: 0.25rem !important;
    font-size: 0.8rem !important;
  }

  .table-wrapper td:first-child,
  .table-wrapper th:first-child {
    min-width: 100px !important;
    max-width: 120px !important;
    padding-left: 0.5rem !important;
  }

  .table-actions {
    padding: 0.75rem !important;
    gap: 0.5rem !important;
  }

  .table-actions .btn {
    padding: 0.6rem !important;
    font-size: 0.85rem !important;
  }
}
